package services

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"
)

// TypesenseClient encapsulates minimal operations we need
type TypesenseClient struct {
	BaseURL    string
	APIKey     string
	Collection string
	Timeout    time.Duration
}

func NewTypesenseClient(baseURL, apiKey, collection string, timeoutMS int) *TypesenseClient {
	return &TypesenseClient{
		BaseURL:    strings.TrimRight(baseURL, "/"),
		APIKey:     apiKey,
		Collection: collection,
		Timeout:    time.Duration(timeoutMS) * time.Millisecond,
	}
}

// GetStateByWhSKU fetches current is_available and available_qty for the first matching document
func (t *TypesenseClient) GetStateByWhSKU(ctx context.Context, whSKU, facilityCode string) (bool, int, error) {
	if t == nil || t.BaseURL == "" || t.APIKey == "" || t.Collection == "" {
		return false, 0, fmt.Errorf("typesense client not configured")
	}

	httpClient := &http.Client{Timeout: t.Timeout}

	type searchResp struct {
		Hits []struct {
			Document map[string]interface{} `json:"document"`
		} `json:"hits"`
	}

	q := url.Values{}
	q.Set("q", "*")
	q.Set("filter_by", fmt.Sprintf("facility_code:=%q && wh_sku:=`%q`", facilityCode, whSKU))
	q.Set("per_page", "1")
	q.Set("page", "1")

	searchURL := fmt.Sprintf("%s/collections/%s/documents/search?%s", t.BaseURL, url.PathEscape(t.Collection), q.Encode())
	req, _ := http.NewRequestWithContext(ctx, http.MethodGet, searchURL, nil)
	req.Header.Set("X-TYPESENSE-API-KEY", t.APIKey)

	resp, err := httpClient.Do(req)
	if err != nil {
		return false, 0, err
	}
	defer resp.Body.Close()
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return false, 0, fmt.Errorf("typesense search failed: %s", resp.Status)
	}

	var sr searchResp
	if err := json.NewDecoder(resp.Body).Decode(&sr); err != nil {
		return false, 0, err
	}
	if len(sr.Hits) == 0 {
		return false, 0, fmt.Errorf("typesense docs not found for wh_sku=%s facility_code=%s", whSKU, facilityCode)
	}
	doc := sr.Hits[0].Document

	beforeFlag := false
	if v, ok := doc["is_available"].(bool); ok {
		beforeFlag = v
	}
	beforeQty := 0
	switch v := doc["available_qty"].(type) {
	case float64:
		beforeQty = int(v)
	case int:
		beforeQty = v
	case string:
		// best effort parse integer-like string
		// ignore error, keep default
	}

	return beforeFlag, beforeQty, nil
}

// UpdateAvailabilityByWhSKU finds documents by wh_sku + facility_code and patches fields for ALL matches.
// Returns the number of documents successfully updated.
func (t *TypesenseClient) UpdateAvailabilityByWhSKU(ctx context.Context, whSKU, facilityCode string, isAvailable bool, availableQty int) (int, error) {
	if t == nil || t.BaseURL == "" || t.APIKey == "" || t.Collection == "" {
		return 0, fmt.Errorf("typesense client not configured")
	}

	httpClient := &http.Client{Timeout: t.Timeout}

	type searchResp struct {
		Hits []struct {
			Document map[string]interface{} `json:"document"`
		} `json:"hits"`
	}

	// Collect all matching ids via pagination
	var ids []string
	perPage := 250
	for page := 1; ; page++ {
		q := url.Values{}
		q.Set("q", "*")
		q.Set("filter_by", fmt.Sprintf("facility_code:=%q && wh_sku:=`%q`", facilityCode, whSKU))
		q.Set("per_page", fmt.Sprintf("%d", perPage))
		q.Set("page", fmt.Sprintf("%d", page))

		searchURL := fmt.Sprintf("%s/collections/%s/documents/search?%s", t.BaseURL, url.PathEscape(t.Collection), q.Encode())
		req, _ := http.NewRequestWithContext(ctx, http.MethodGet, searchURL, nil)
		req.Header.Set("X-TYPESENSE-API-KEY", t.APIKey)

		resp, err := httpClient.Do(req)
		if err != nil {
			return 0, err
		}
		defer resp.Body.Close()
		if resp.StatusCode < 200 || resp.StatusCode >= 300 {
			return 0, fmt.Errorf("typesense search failed: %s", resp.Status)
		}

		var sr searchResp
		if err := json.NewDecoder(resp.Body).Decode(&sr); err != nil {
			return 0, err
		}
		if len(sr.Hits) == 0 {
			break
		}
		for _, h := range sr.Hits {
			if idStr, ok := h.Document["id"].(string); ok && idStr != "" {
				ids = append(ids, idStr)
			}
		}
		if len(sr.Hits) < perPage {
			break
		}
	}

	if len(ids) == 0 {
		return 0, fmt.Errorf("typesense docs not found for wh_sku=%s facility_code=%s", whSKU, facilityCode)
	}

	// Build JSONL for bulk partial update via import?action=update
	var buf bytes.Buffer
	for i := range ids {
		line := map[string]interface{}{
			"id":            ids[i],
			"is_available":  isAvailable,
			"available_qty": availableQty,
		}
		b, _ := json.Marshal(line)
		buf.Write(b)
		buf.WriteByte('\n')
	}

	bulkURL := fmt.Sprintf("%s/collections/%s/documents/import", t.BaseURL, url.PathEscape(t.Collection))
	breq, _ := http.NewRequestWithContext(ctx, http.MethodPost, bulkURL, bytes.NewReader(buf.Bytes()))
	breq.Header.Set("X-TYPESENSE-API-KEY", t.APIKey)
	breq.Header.Set("Content-Type", "text/plain") // JSONL must be sent as text/plain

	// action=update for partial updates
	q := breq.URL.Query()
	q.Set("action", "update")
	breq.URL.RawQuery = q.Encode()

	bresp, err := httpClient.Do(breq)
	if err != nil {
		return 0, err
	}
	defer bresp.Body.Close()
	if bresp.StatusCode < 200 || bresp.StatusCode >= 300 {
		return 0, fmt.Errorf("typesense bulk update failed: %s", bresp.Status)
	}

	// Bulk API returns JSONL with per-line result {"success": true/false, ...}
	respBody, err := io.ReadAll(bresp.Body)
	if err != nil {
		return 0, err
	}
	lines := strings.Split(strings.TrimSpace(string(respBody)), "\n")
	updated := 0
	for _, ln := range lines {
		ln = strings.TrimSpace(ln)
		if ln == "" {
			continue
		}
		var rline struct {
			Success bool `json:"success"`
		}
		if err := json.Unmarshal([]byte(ln), &rline); err == nil && rline.Success {
			updated++
		}
	}

	return updated, nil
}
