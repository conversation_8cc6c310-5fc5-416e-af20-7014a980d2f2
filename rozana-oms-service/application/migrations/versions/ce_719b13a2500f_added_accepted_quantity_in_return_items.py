"""added accepted quantity in return items

Revision ID: 719b13a2500f
Revises: 4c93024bc3ba
Create Date: 2025-09-16 04:16:34.335743

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '719b13a2500f'
down_revision: Union[str, Sequence[str], None] = '4c93024bc3ba'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('return_items', sa.Column('accepted_quantity', sa.Integer(), server_default='0', nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('return_items', 'accepted_quantity')
    # ### end Alembic commands ###
