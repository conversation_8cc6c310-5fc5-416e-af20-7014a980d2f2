"""added unfullfilled_quantity column

Revision ID: 3101e853c140
Revises: eda06983c5ba
Create Date: 2025-09-10 17:27:25.936630

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '3101e853c140'
down_revision: Union[str, Sequence[str], None] = 'eda06983c5ba'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('order_items', sa.Column('unfulfilled_quantity', sa.DECIMAL(precision=10, scale=2), server_default='0', nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('order_items', 'unfulfilled_quantity')
    # ### end Alembic commands ###
