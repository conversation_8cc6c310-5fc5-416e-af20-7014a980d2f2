"""add_invoice_key

Revision ID: eda06983c5ba
Revises: 3590a59c2eeb
Create Date: 2025-09-07 11:47:50.111161

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'eda06983c5ba'
down_revision: Union[str, Sequence[str], None] = '3590a59c2eeb'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('orders', sa.Column('invoice_key', sa.String(length=1024), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('orders', 'invoice_key')
    # ### end Alembic commands ###
