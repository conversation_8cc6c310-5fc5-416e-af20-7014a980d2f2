import os
import logging
from typing import Dict, List, Tuple
from app.services.typesense_service import TypesenseService
from app.dto.orders import OrderItemCreate

# Logger
from app.logging.utils import get_app_logger
logger = get_app_logger(__name__)

# Settings
from app.config.settings import OMSConfigs
configs = OMSConfigs()

PRICE_CHECK_ENABLED = configs.PRICE_CHECK_ENABLED

class TypesenseValidator:
    def __init__(self):
        self.typesense_service = TypesenseService()

    async def validate_wh_sku_and_pack_uom_quantity(self, product: Dict) -> bool:
        wh_sku = product.get("wh_sku")
        pack_uom_quantity = product.get("pack_uom_qty")
        facility_name = product.get("facility_code")
        sku = product.get("child_sku")

        if not wh_sku or not pack_uom_quantity:
            raise ValueError(f"wh_sku or pack_uom_qty not available for facility {facility_name} and sku {sku}")

    async def validate_product_price(self, product: Dict, payload_price: float) -> bool:
        # check if PRICE_CHECK_ENABLED is enabled
        if not PRICE_CHECK_ENABLED:
            return True

        typesense_price = product.get("selling_price")
        facility_name = product.get("facility_code")
        sku = product.get("child_sku")

        if not typesense_price:
            raise ValueError(f"No price found in Typesense for SKU {sku} and facility {facility_name}")

        # validate price format
        try:
            typesense_price = float(typesense_price)
        except (ValueError, TypeError):
            raise ValueError(f"Invalid price format in Typesense for SKU {sku} and facility {facility_name}")

        # validate price value
        price_match = abs(payload_price - typesense_price) < 0.01
        if not price_match:
            raise ValueError(f"Price mismatch for SKU {sku}") #: payload={payload_price}, actual={typesense_price}")

        return price_match

    async def validate_freebie_eligibility(self, sku: str, facility_name: str, order_amount: float) -> bool:
        """Validate if order amount meets freebie minimum requirement"""
        try:
            freebie = await self.typesense_service.get_freebie_by_sku(sku, facility_name)
            if not freebie:
                raise ValueError(f"Freebie item {sku} not found for facility {facility_name}")
            
            freebie_min_amount = float(freebie.get("amount", 0))
            
            if order_amount < freebie_min_amount:
                raise ValueError(f"Order amount {order_amount} is less than required amount {freebie_min_amount} for freebie {sku}")
            
            # Check if freebie is currently active (optional date validation)
            import time
            current_time = int(time.time())
            start_date = freebie.get("start_date")
            end_date = freebie.get("end_date")
            
            if start_date and current_time < start_date:
                raise ValueError(f"Freebie {sku} is not yet active (starts at {start_date})")
            
            if end_date and current_time > end_date:
                raise ValueError(f"Freebie {sku} has expired (ended at {end_date})")
            
            # Check available quantity
            available_qty = freebie.get("available_qty", 0)
            if available_qty <= 0:
                raise ValueError(f"Freebie {sku} is out of stock")
            
            logger.info(f"Freebie {sku} validation passed for order amount {order_amount}")
            return freebie
            
        except Exception as e:
            logger.error(f"Error validating freebie {sku}: {str(e)}", exc_info=True)
            raise e

    async def validate_items(self, items: List[OrderItemCreate], facility_name: str, order_amount: float = None, origin: str = "app") -> Tuple[List[str], Dict[str, Dict]]:
        # here we have to collect all the errors at once and guve to the response not just first seen error
        errors = []
        products = {}
        for item in items:
            try:
                # Check if this is a freebie item (price is zero)
                is_freebie = item.sale_price == 0.0
                if is_freebie:
                    # For freebie items, validate against freebies_products collection
                    if not facility_name or order_amount is None:
                        errors.append(f"Facility name and order amount required for freebie validation of {item.sku}")
                        continue
                    
                    # Validate freebie eligibility
                    product = await self.validate_freebie_eligibility(item.sku, facility_name, order_amount)
                    
                    if not product:
                        errors.append(f"Product {item.sku} Not Found for facility {facility_name}")
                        continue
                    
                    # Skip price validation for freebie items but validate other fields
                    await self.validate_wh_sku_and_pack_uom_quantity(product)
                    products[item.sku] = product
                else:
                    # Regular product validation
                    product = await self.typesense_service.get_product_by_sku(item.sku, facility_name, origin=origin, mrp = item.unit_price)
                    if not product:
                        errors.append(f"Product {item.sku} Not Found for facility {facility_name}")
                        continue

                    # Validate Each item
                    await self.validate_wh_sku_and_pack_uom_quantity(product)
                    await self.validate_product_price(product, item.sale_price)
                    products[item.sku] = product
            except Exception as e:
                errors.append(str(e))
        return products, errors

    async def enrich_items(self, items: List[OrderItemCreate],  products: Dict[str, Dict], facility_name: str,) -> List[Dict]:
        enriched_items = []
        errors = []
        for item in items:
            try:
                product = products.get(item.sku)
                if not product:
                    raise ValueError(f"Product {item.sku} Not Found for facility {facility_name}")

                # Extract only required fields from product
                product_fields = self.typesense_service.extract_item_fields(product)
                enriched_item = {
                    "sku": item.sku,
                    "quantity": item.quantity,
                    "pos_extra_quantity": getattr(item, "pos_extra_quantity", 0),
                    "unit_price": item.unit_price,
                    "sale_price": item.sale_price,
                    **product_fields
                }
                enriched_items.append(enriched_item)
            except Exception as e:
                logger.error(f"Error enriching item {item.sku}: {str(e)}", exc_info=True)
                errors.append(str(e))
        return enriched_items, errors

