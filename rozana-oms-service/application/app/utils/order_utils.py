from app.core.constants import OrderStatus
from app.validations.stock import StockValidator
from app.services.typesense_service import TypesenseService
from app.config.settings import OMSConfigs

from fastapi import BackgroundTasks
from datetime import datetime, timedelta
from app.models.common import get_ist_now

# Logger
from app.logging.utils import get_app_logger
logger = get_app_logger("app.utils.order_utils")

configs = OMSConfigs()
cap_qty = configs.CAP_QUANTITY
safety_qty = configs.SAFETY_QUANTITY
update_typesense = configs.UPDATE_TYPESENSE_ENABLED
target_collection = configs.TYPESENSE_COLLECTION_NAME


def get_ist(datetime_str: str):
    if datetime_str:
        dt_time = datetime.fromisoformat(datetime_str.replace('Z', '+00:00'))
    else:
        dt_time = get_ist_now()
    return dt_time


def can_cancel_order(current_status: int) -> bool:
    if current_status in [OrderStatus.CANCELED, OrderStatus.CANCELLED_PENDING_REFUND]:
        return False

    can_cancel = [
        OrderStatus.DRAFT,           # 0
        OrderStatus.OPEN,            # 10
        OrderStatus.WMS_SYNCED,      # 21
        OrderStatus.WMS_SYNC_FAILED, # 22
        OrderStatus.WMS_OPEN,        # 23
        OrderStatus.WMS_INPROGRESS   # 24
    ]

    return current_status in can_cancel

async def block_quantity_in_redis(enriched_items, facility_name):
    logger.info(f"Starting block_quantity_in_redis for facility: {facility_name}, items count: {len(enriched_items)}")
    bulk_update_data = []
    for item in enriched_items:
        logger.info(f"Processing item: wh_sku={item.get('wh_sku')}, quantity={item.get('quantity')}, pack_uom_quantity={item.get('pack_uom_quantity')}")
        stock_validator = StockValidator(facility_name, item["wh_sku"])
        ims_new_available_stock = stock_validator.block_stock(item["quantity"] * item["pack_uom_quantity"])
        logger.info(f"After blocking stock for wh_sku={item['wh_sku']}: ims_new_available_stock={ims_new_available_stock}")
        
        if update_typesense == 'false':
            logger.info(f"Typesense update disabled, skipping for wh_sku={item['wh_sku']}")
            continue
        
        typesense_qty = item.get("available_qty")
        logger.info(f"Typesense current qty for wh_sku={item['wh_sku']}: {typesense_qty}")
        
        if ims_new_available_stock > safety_qty:
            new_typesense_qty = min(ims_new_available_stock - safety_qty, cap_qty)
            new_availability = True
        else:
            new_typesense_qty = 0
            new_availability = False
        
        logger.info(f"Calculated new values for wh_sku={item['wh_sku']}: new_typesense_qty={new_typesense_qty}, new_availability={new_availability}")
        
        if typesense_qty != new_typesense_qty:
            update_item = {
                'id': item.get("document_id"),
                'is_available': new_availability,
                'available_qty': new_typesense_qty,
                'wh_sku': item.get("wh_sku"),
                'unit_price': item.get("unit_price")
            }
            bulk_update_data.append(update_item)
            logger.info(f"Added to bulk update for wh_sku={item['wh_sku']}: {update_item}")
        else:
            logger.info(f"No change needed for wh_sku={item['wh_sku']}, current={typesense_qty}, new={new_typesense_qty}")
    
    logger.info(f"block_quantity_in_redis completed, bulk_update_data count: {len(bulk_update_data)}")
    return bulk_update_data

async def block_quantity_in_typesense(bulk_update_data, facility_name, origin: str = "app"):
    # these can be multiple docs for one wh_sku so we need to update all of them
    # we have to prepare the search for all wh_sku from bulk_update_data and get the docs
    typesense = TypesenseService()
    wh_skus = [item["wh_sku"] for item in bulk_update_data]
    mrps = [item["unit_price"] for item in bulk_update_data]
    logger.info(f"Fetching Typesense documents for wh_skus: {wh_skus}")
    
    try:
        typesense_docs = await typesense.get_document_by_wh_skus(wh_skus, collection=target_collection, facility_name=facility_name, origin=origin, mrps=mrps)
        logger.info(f"Retrieved {len(typesense_docs)} documents from Typesense for facility: {facility_name}")
    except Exception as e:
        logger.error(f"Failed to fetch documents from Typesense: {str(e)}")
        raise

    # now we got the typesense_docs now map the typesense_docs to bulk_update_data on wh_sku and populate the same is_available field and available_qty field
    bulk_update_data_by_wh_sku = {}
    for item in bulk_update_data:
        bulk_update_data_by_wh_sku[item["wh_sku"]] = item

    updated_docs = []
    for doc in typesense_docs:
        if doc["wh_sku"] in bulk_update_data_by_wh_sku:
            item = bulk_update_data_by_wh_sku[doc["wh_sku"]]
            prepare_for_update = {
                "id": doc["id"],
                "is_available": item["is_available"],
                "available_qty": item["available_qty"]
            }
            updated_docs.append(prepare_for_update)
            logger.info(f"Prepared update for document id={doc['id']}, wh_sku={doc['wh_sku']}: {prepare_for_update}")
        else:
            logger.warning(f"Document wh_sku={doc['wh_sku']} not found in bulk_update_data")
    
    logger.info(f"Prepared {len(updated_docs)} documents for bulk update in Typesense")
    
    if updated_docs:
        try:
            await typesense.bulk_update_documents(updated_docs, collection=target_collection)
            logger.info(f"Successfully updated {len(updated_docs)} documents in Typesense collection: {target_collection}")
        except Exception as e:
            logger.error(f"Failed to bulk update documents in Typesense: {str(e)}")
            raise
    else:
        logger.warning("No documents to update in Typesense")