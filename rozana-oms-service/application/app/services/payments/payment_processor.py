# Services
from app.services.wallet_payment_service import WalletPaymentService
from app.services.payment_service import PaymentService

# Repository
from app.repository.payments import PaymentRepository

# Constants
from app.core.constants import PaymentStatus

import json

# Logger
from app.logging.utils import get_app_logger
logger = get_app_logger("order_payment_processor")

class OrderPaymentProcessor:
    def __init__(self):
        pass
    
    async def process_order_payment(self, order_id: str, payment_records = [], customer_id: str = None):

        # Single Payment Mode
        # if its wallet only call wallet and debut the amount 
        # if its only cash the make the status as completed

        ## Combinations Payment Mode
        # its wallet + cash debit the amount from wallet and make the status as completed

        # checking all the payment modes skiping order sync when razorpay is present
        sync_order = False
        payment_modes = []
        for payment_record in payment_records:
            mode = payment_record.get("payment_mode", "").lower()
            payment_modes.append(mode)

        # if "razorpay" in payment_mode:
        if "razorpay" in payment_modes:
            return True, sync_order

        # sort the payment records based on the payment mode # to be checked

        try:
            description = "Payment for order " + order_id
            for payment_record in payment_records:
                if payment_record.get("payment_mode", "").lower() == "wallet":
                    wallet_payment_service = WalletPaymentService()
                    payment_id = payment_record.get("payment_id")
                    amount = payment_record.get("amount")
                    wallet_result = await wallet_payment_service.process_wallet_payment(customer_id=customer_id,
                        order_id=order_id,
                        amount=amount,
                        payment_id=payment_id, 
                        description=description
                    )
                    logger.info(f"Wallet payment processed successfully: {payment_id}")
                    if not wallet_result.get("success", False):
                        logger.error(f"Wallet payment failed: {json.dumps(wallet_result)}")
                        return False, sync_order

                elif payment_record.get("payment_mode", "").lower() == "cash":
                    payment_id = payment_record.get("payment_id")
                    payment_service = PaymentService()
                    cash_result = await payment_service.update_payment_status(
                        payment_id=payment_id,
                        new_status=PaymentStatus.COMPLETED
                    )
                    logger.info(f"Cash payment processed successfully: {payment_id}")
                    if not cash_result.get("success", False):
                        logger.error(f"Cash payment failed: {json.dumps(cash_result)}")
                        return False, sync_order

            sync_order = True
            return True, sync_order

        except Exception as e:
            logger.error(f"Error processing order payment: {str(e)}")
            return False, sync_order

    async def process_razorpay_included_order_payment(self, order_id: str, payment_records = [], razorpay_payment_id: str = None):

        # if the payment mode is razorpay then update the payment status as completed
        # if the payment mode is razorpay and cash then update the payment status as completed
        # if the payment mode is razorpay and wallet debit the amount from wallet and make the status as completed
        # Three Combinations
        # 1. razorpay + cash + wallet - debit the amount from wallet and make the status as completed

        sync_order = False
        # sort the payment records based on the payment mode # to be checked

        try:
            description = "Payment for order " + order_id
            for payment_record in payment_records:
                if payment_record.get("payment_mode", "").lower() == "wallet":
                    wallet_payment_service = WalletPaymentService()
                    payment_id = payment_record.get("payment_id")
                    amount = float(payment_record.get("payment_amount"))
                    customer_id = payment_record.get("customer_id")
                    wallet_result = await wallet_payment_service.process_wallet_payment(customer_id=customer_id,
                        order_id=order_id,
                        amount=amount,
                        payment_id=payment_id,
                        description=description
                    )
                    logger.info(f"Wallet payment processed successfully: {payment_id}")
                    if not wallet_result.get("success", False):
                        logger.error(f"Wallet payment failed: {json.dumps(wallet_result)}")
                        return False, sync_order

                elif payment_record.get("payment_mode", "").lower() == "cash":
                    payment_id = payment_record.get("payment_id")
                    payment_service = PaymentService()
                    cash_result = await payment_service.update_payment_status(
                        payment_id=payment_id,
                        new_status=PaymentStatus.COMPLETED
                    )
                    logger.info(f"Cash payment processed successfully: {payment_id}")
                    if not cash_result.get("success", False):
                        logger.error(f"Cash payment failed: {json.dumps(cash_result)}")
                        return False, sync_order

                elif payment_record.get("payment_mode", "").lower() == "razorpay":
                    payment_id = razorpay_payment_id
                    payment_internal_id = payment_record.get("id")
                    # update the payment_id in the payment_details table
                    payment_repo = PaymentRepository()
                    payment_repo.upadate_the_razorpay_payment_id(payment_internal_id, payment_id)
                    payment_service = PaymentService()
                    razorpay_result = await payment_service.update_payment_status(
                        payment_id=payment_id,
                        new_status=PaymentStatus.COMPLETED
                    )
                    logger.info(f"Razorpay payment processed successfully: {payment_id}")
                    if not razorpay_result.get("success", False):
                        logger.error(f"Razorpay payment failed: {json.dumps(razorpay_result)}")
                        return False, sync_order

            sync_order = True
            return True, sync_order

        except Exception as e:
            logger.error(f"Error processing order payment: {str(e)}")
            return False, sync_order

        