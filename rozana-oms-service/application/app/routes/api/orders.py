from fastapi import <PERSON><PERSON><PERSON><PERSON>, Query, Header, Request, BackgroundTasks
from pydantic import BaseModel

from app.core.token_validation_core import (
    validate_token_and_get_orders,
    validate_token_and_get_order_items,
    validate_token_and_cancel_order,
    get_orders_by_phone_number,
)
from app.core.order_functions import get_all_orders_core, get_order_details_core, create_order_core
from app.dto.phone_validations import validate_phone_number

from app.dto.orders import OrderCreate, OrderResponse

api_router = APIRouter(tags=["api"])

class CancelOrderRequest(BaseModel):
    customer_id: str
    order_id: str

@api_router.post("/create_order", response_model=OrderResponse)
async def create_order(
    order: OrderCreate,
    request: Request,
    background_tasks: BackgroundTasks,
    authorization: str = Header(..., description="Token for authentication"),
):
    """Create order via API with token-based authentication."""
    return await create_order_core(order, request, background_tasks, "api")

@api_router.get("/get_orders")
async def get_orders(
    customer_id: str = Query(..., description="Customer ID"),
    page_size: int = Query(20, description="Number of orders to return per page"),
    page: int = Query(1, description="Page number (starting from 1)"),
    sort_order: str = Query("desc", description="Sort order (asc/desc)"),
    search: str = Query(None, description="Search orders by order ID"),
    authorization: str = Header(..., description="Token for authentication")
):
    return await validate_token_and_get_orders(
        token=authorization,
        customer_id=customer_id,
        page_size=page_size,
        page=page,
        sort_order=sort_order,
        search=search
    )

@api_router.get("/get_orders_by_phone_number")
async def get_orders_by_phone(
    phone_number: str = Query(..., description="Phone number (10 digits, 91+10 digits, or +91+10 digits)"),
    page_size: int = Query(20, description="Number of orders to return per page"),
    page: int = Query(1, description="Page number (starting from 1)"),
    sort_order: str = Query("desc", description="Sort order (asc/desc)"),
    search: str = Query(None, description="Search orders by order ID"),
    authorization: str = Header(..., description="Token for authentication")
):
    # Validate phone number format
    validated_phone = validate_phone_number(phone_number)
    
    return await get_orders_by_phone_number(
        phone_number=validated_phone,
        page_size=page_size,
        page=page,
        sort_order=sort_order,
        search=search
    )

@api_router.get("/order_details")
async def get_order_details(order_id: str = Query(..., description="Order ID")):
    return await get_order_details_core(order_id)

@api_router.get("/order_items")
async def get_order_items(
    customer_id: str = Query(..., description="Customer ID"),
    order_id: str = Query(..., description="Order ID to get items for"),
    authorization: str = Header(..., description="Token for authentication")
):
    return await validate_token_and_get_order_items(
        token=authorization,
        customer_id=customer_id,
        order_id=order_id
    )

@api_router.post("/cancel_order")
async def cancel_order(
    cancel_request: CancelOrderRequest,
    authorization: str = Header(..., description="Token for authentication")
):
    return await validate_token_and_cancel_order(
        token=authorization,
        customer_id=cancel_request.customer_id,
        order_id=cancel_request.order_id
    )
