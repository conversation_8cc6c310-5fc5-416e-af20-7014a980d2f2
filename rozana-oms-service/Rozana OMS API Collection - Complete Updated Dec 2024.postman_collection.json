{"info": {"_postman_id": "8dda77d2-6d0e-46fd-93ca-2d64b04d08ea", "name": "Rozana OMS API Collection - Complete Updated Dec 2024", "description": "Complete and updated API collection for Rozana Order Management System (OMS) - includes ALL available endpoints with exact parameters and request bodies matching the codebase. Updated with missing /api/v1/create_order endpoint and corrected parameter names.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "45698663", "_collection_link": "https://oms999-4975.postman.co/workspace/My-Workspace~857b6ecc-fa98-4b75-8398-7df51ec78be2/collection/45698663-8dda77d2-6d0e-46fd-93ca-2d64b04d08ea?action=share&source=collection_link&creator=45698663"}, "item": [{"name": "Health Check", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/health", "host": ["{{baseUrl}}"], "path": ["health"]}, "description": "Check the health status of the OMS service"}, "response": []}]}, {"name": "APP APIs (Firebase App Token)", "item": [{"name": "Get All Orders", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "{{app_token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/app/v1/orders?page=1&sort_order=desc", "host": ["{{baseUrl}}"], "path": ["app", "v1", "orders"], "query": [{"key": "page_size", "value": "7", "disabled": true}, {"key": "page", "value": "1"}, {"key": "sort_order", "value": "desc"}, {"key": "search", "value": "7", "disabled": true}, {"key": "exclude_status", "value": "10", "disabled": true}]}, "description": "Get all orders for the authenticated mobile user (pagination supported)"}, "response": []}, {"name": "Get Order Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{app_token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/app/v1/order_details?order_id={{sample_order_id}}", "host": ["{{baseUrl}}"], "path": ["app", "v1", "order_details"], "query": [{"key": "order_id", "value": "{{sample_order_id}}"}]}, "description": "Get detailed information about a specific order"}, "response": []}, {"name": "Order Again", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{app_token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/app/v1/order_again?page_size=20&page=1", "host": ["{{baseUrl}}"], "path": ["app", "v1", "order_again"], "query": [{"key": "page_size", "value": "20"}, {"key": "page", "value": "1"}]}, "description": "Get products for order again functionality"}, "response": []}, {"name": "Create Order", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{app_token}}", "type": "text", "disabled": true}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{api_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"customer_id\": \"{{customer_id}}\",\n  \"customer_name\": \"PNVS\",\n  \"facility_id\": \"5883\",\n  \"facility_name\": \"ROZANA_TEST_WH1\",\n  \"status\": \"pending\",\n  \"total_amount\": 299.99,\n  \"is_approved\": true,\n  \"items\": [\n    {\n      \"sku\": \"ROZ1205-1PCS\",\n      \"quantity\": 1.00,\n      \"unit_price\": 40.00,\n      \"sale_price\": 38.00\n    },\n    {\n      \"sku\": \"ROZ10822-1PCS\",\n      \"name\": \"Sample Product\",\n      \"quantity\": 2.00,\n      \"unit_price\": 110,\n      \"sale_price\": 55\n    }\n  ],\n  \"address\": {\n    \"full_name\": \"<PERSON>rdha<PERSON>\",\n    \"phone_number\": \"9110345323\",\n    \"address_line1\": \"123 Main Street\",\n    \"address_line2\": \"Apt 4B\",\n    \"city\": \"Mumbai\",\n    \"state\": \"Maharashtra\",\n    \"postal_code\": \"400001\",\n    \"country\": \"india\",\n    \"type_of_address\": \"home\",\n    \"longitude\": 72.8777,\n    \"latitude\": 19.0760\n  },\n  \"payment\": [\n    {\n      \"payment_mode\": \"cod\",\n      \"create_payment_order\": false,\n      \"amount\": 299.99\n    }\n  ],\n  \"eta\": \"2024-12-15T14:30:00Z\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/create_order", "host": ["{{baseUrl}}"], "path": ["api", "v1", "create_order"]}, "description": "Create a new order via mobile app"}, "response": []}, {"name": "Encrypt Customer Code", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{app_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"customer_code\": \"CUST123\"\n}"}, "url": {"raw": "{{baseUrl}}/app/v1/encrypt_customer_code", "host": ["{{baseUrl}}"], "path": ["app", "v1", "encrypt_customer_code"]}, "description": "Encrypt customer code using AES encryption"}, "response": []}, {"name": "Update Order Status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{app_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"order_id\": \"{{sample_order_id}}\",\n  \"status\": \"confirmed\"\n}"}, "url": {"raw": "{{baseUrl}}/app/v1/update_order_status", "host": ["{{baseUrl}}"], "path": ["app", "v1", "update_order_status"]}, "description": "Update the overall status of an order"}, "response": []}, {"name": "Update Item Status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{app_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"order_id\": \"{{sample_order_id}}\",\n  \"sku\": \"ROZ13792-40-COTTONBLEND-CORE-1-DAILYWEAR\",\n  \"status\": \"confirmed\"\n}"}, "url": {"raw": "{{baseUrl}}/app/v1/update_item_status", "host": ["{{baseUrl}}"], "path": ["app", "v1", "update_item_status"]}, "description": "Update the status of a specific item in an order"}, "response": []}, {"name": "Cancel Order", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{app_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"order_id\": \"{{sample_order_id}}\"\n}"}, "url": {"raw": "{{baseUrl}}/app/v1/cancel_order", "host": ["{{baseUrl}}"], "path": ["app", "v1", "cancel_order"]}, "description": "Cancel an existing order"}, "response": []}, {"name": "Create Return", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{app_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"order_id\": \"{{sample_order_id}}\",\n  \"return_reason_code\": \"DAMAGED\",\n  \"comments\": \"Product was defective\",\n  \"order_full_return\": false,\n  \"items\": [\n    {\n      \"sku\": \"ROZ14937-1PCS\",\n      \"quantity\": 4\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/app/v1/create_return", "host": ["{{baseUrl}}"], "path": ["app", "v1", "create_return"]}, "description": "Create a return request for specific items or full order"}, "response": []}, {"name": "Get Return Reasons", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{app_token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/app/v1/returns/reasons?order_id={{sample_order_id}}", "host": ["{{baseUrl}}"], "path": ["app", "v1", "returns", "reasons"], "query": [{"key": "order_id", "value": "{{sample_order_id}}"}]}, "description": "Get list of available return reasons"}, "response": []}]}, {"name": "Payment APIs", "item": [{"name": "Create Payment Order", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{app_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"order_id\": \"{{sample_order_id}}\",\n  \"amount\": 299.99,\n  \"customer_id\": \"{{customer_id}}\",\n  \"customer_name\": \"<PERSON>\",\n  \"customer_email\": \"<EMAIL>\",\n  \"customer_phone\": \"+91-9876543210\",\n  \"notes\": {\n    \"description\": \"Payment for order {{sample_order_id}}\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/app/v1/create_payment_order", "host": ["{{baseUrl}}"], "path": ["app", "v1", "create_payment_order"]}, "description": "Create a Razorpay payment order for processing"}, "response": []}, {"name": "Verify Payment", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{app_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"oms_order_id\": \"{{sample_order_id}}\",\n  \"razorpay_order_id\": \"order_sample123\",\n  \"razorpay_payment_id\": \"pay_sample123\",\n  \"razorpay_signature\": \"sample_signature_hash\"\n}"}, "url": {"raw": "{{baseUrl}}/app/v1/verify_payment", "host": ["{{baseUrl}}"], "path": ["app", "v1", "verify_payment"]}, "description": "Verify payment signature and update order status"}, "response": []}, {"name": "Get Payment Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{app_token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/app/v1/payment_status/{{sample_order_id}}", "host": ["{{baseUrl}}"], "path": ["app", "v1", "payment_status", "{{sample_order_id}}"]}, "description": "Get payment status for a specific order"}, "response": []}]}, {"name": "POS APIs (Firebase POS Token)", "item": [{"name": "Get All Orders", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{pos_token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/pos/v1/orders?facility_name=ROZANA_TEST_WH1&page_size=20&page=1&sort_order=desc&order_id=3&phone_number=9110345323&customer_name=&order_mode=", "host": ["{{baseUrl}}"], "path": ["pos", "v1", "orders"], "query": [{"key": "facility_name", "value": "ROZANA_TEST_WH1"}, {"key": "page_size", "value": "20"}, {"key": "page", "value": "1"}, {"key": "sort_order", "value": "desc"}, {"key": "order_id", "value": "3"}, {"key": "phone_number", "value": "9110345323"}, {"key": "customer_name", "value": ""}, {"key": "order_mode", "value": ""}]}, "description": "Get all orders for a specific facility via POS"}, "response": []}, {"name": "Get Order Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{pos_token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/pos/v1/order_details?order_id={{sample_order_id}}&facility_name={{facility_name}}", "host": ["{{baseUrl}}"], "path": ["pos", "v1", "order_details"], "query": [{"key": "order_id", "value": "{{sample_order_id}}"}, {"key": "facility_name", "value": "{{facility_name}}"}]}, "description": "Get detailed information about a specific order via POS"}, "response": []}, {"name": "Create Order", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{pos_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n//   \"customer_id\": \"{{customer_id}}\",\n  \"customer_name\": \"guest\",\n  \"facility_id\": \"FAC001\",\n  \"facility_name\": \"{{facility_name}}\",\n  \"status\": \"pending\",\n  \"total_amount\": 199.99,\n  \"is_approved\": true,\n  \"items\": [\n    {\n      \"sku\": \"ROZ1205-1PCS\",\n      \"quantity\": 1,\n      \"unit_price\": 40,\n      \"sale_price\": 38\n    },\n    {\n      \"sku\": \"ROZ10822-1PCS\",\n      \"name\": \"Sample Product\",\n      \"quantity\": 2.00,\n      \"unit_price\": 110,\n      \"sale_price\": 55,\n      \"pos_extra_quantity\": 0\n    }\n  ],\n  \"address\": {\n    \"full_name\": \"<PERSON>rdhav\",\n    \"phone_number\": \"9110345323\",\n    \"address_line1\": \"POS Test Address\",\n    \"address_line2\": \"Suite 100\",\n    \"city\": \"Mumbai\",\n    \"state\": \"Maharashtra\",\n    \"postal_code\": \"400001\",\n    \"country\": \"india\",\n    \"type_of_address\": \"work\",\n    \"longitude\": 72.8777,\n    \"latitude\": 19.0760\n  },\n  \"payment\": [\n    {\n      \"payment_mode\": \"cash\",\n      \"create_payment_order\": false,\n      \"amount\": 199.99\n    }\n    ],\n  \"eta\": \"2024-12-15T16:00:00Z\"\n}"}, "url": {"raw": "{{baseUrl}}/pos/v1/create_order", "host": ["{{baseUrl}}"], "path": ["pos", "v1", "create_order"]}, "description": "Create a new order via POS system"}, "response": []}, {"name": "Update Order Status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{pos_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"order_id\": \"{{sample_order_id}}\",\n  \"status\": \"confirmed\"\n}"}, "url": {"raw": "{{baseUrl}}/pos/v1/update_order_status", "host": ["{{baseUrl}}"], "path": ["pos", "v1", "update_order_status"]}, "description": "Update the status of an order via POS"}, "response": []}, {"name": "Update Item Status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{pos_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"order_id\": \"{{sample_order_id}}\",\n  \"sku\": \"ROZ13792-40-COTTONBLEND-CORE-1-DAILYWEAR\",\n  \"status\": \"confirmed\"\n}"}, "url": {"raw": "{{baseUrl}}/pos/v1/update_item_status", "host": ["{{baseUrl}}"], "path": ["pos", "v1", "update_item_status"]}, "description": "Update the status of a specific item via POS"}, "response": []}, {"name": "Create Return", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{pos_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"order_id\": \"{{sample_order_id}}\",\n  \"return_reason_code\": \"DEFECTIVE\",\n  \"comments\": \"Product was defective\",\n  \"order_full_return\": false,\n  \"items\": [\n    {\n      \"sku\": \"ROZ1602-1PCS\",\n      \"quantity\": 1\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/pos/v1/create_return", "host": ["{{baseUrl}}"], "path": ["pos", "v1", "create_return"]}, "description": "Create a return request via POS system"}, "response": []}]}, {"name": "API Token Validation Endpoints", "item": [{"name": "Create Order (API Token)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{api_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"customer_id\": \"{{customer_id}}\",\n  \"customer_name\": \"<PERSON>\",\n  \"facility_id\": \"FAC001\",\n  \"facility_name\": \"ROZANA_TEST_WH1\",\n  \"status\": \"pending\",\n  \"total_amount\": 299.99,\n  \"is_approved\": true,\n  \"items\": [\n    {\n      \"sku\": \"ROZ1602-1PCS\",\n      \"name\": \"Sample Product\",\n      \"quantity\": 2,\n      \"unit_price\": 149.99,\n      \"sale_price\": 149.99\n    }\n  ],\n  \"address\": {\n    \"full_name\": \"<PERSON>\",\n    \"phone_number\": \"9110345323\",\n    \"address_line1\": \"123 Main Street\",\n    \"address_line2\": \"Apt 4B\",\n    \"city\": \"Mumbai\",\n    \"state\": \"Maharashtra\",\n    \"postal_code\": \"400001\",\n    \"country\": \"india\",\n    \"type_of_address\": \"home\",\n    \"longitude\": 72.8777,\n    \"latitude\": 19.0760\n  },\n  \"payment\": [\n    {\n      \"payment_mode\": \"cod\",\n      \"create_payment_order\": false,\n      \"amount\": 299.99\n    }\n  ],\n  \"eta\": \"2024-12-15T14:30:00Z\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/create_order", "host": ["{{baseUrl}}"], "path": ["api", "v1", "create_order"]}, "description": "Create a new order via API with token authentication. Requires API token in Authorization header."}, "response": []}, {"name": "Get Orders (Token Validation)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{api_token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/v1/get_orders?customer_id={{customer_id}}&page_size=20&page=1", "host": ["{{baseUrl}}"], "path": ["api", "v1", "get_orders"], "query": [{"key": "customer_id", "value": "{{customer_id}}"}, {"key": "page_size", "value": "20"}, {"key": "page", "value": "1"}, {"key": "sort_order", "value": "desc", "disabled": true}, {"key": "search", "value": "ABCD", "disabled": true}, {"key": "customer_id", "value": "{{cust_988_id}}", "disabled": true}]}, "description": "Get orders after token validation (requires external token validation service)"}, "response": []}, {"name": "Get Order Details (Token Validation)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{api_token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/v1/order_details?order_id={{sample_order_id}}", "host": ["{{baseUrl}}"], "path": ["api", "v1", "order_details"], "query": [{"key": "order_id", "value": "{{sample_order_id}}"}, {"key": "customer_id", "value": "{{cust_988_id}}", "disabled": true}]}, "description": "Get order details after token validation"}, "response": []}, {"name": "Get Order Items", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{api_token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/v1/order_items?customer_id={{customer_id}}&order_id={{sample_order_id}}", "host": ["{{baseUrl}}"], "path": ["api", "v1", "order_items"], "query": [{"key": "customer_id", "value": "{{customer_id}}"}, {"key": "order_id", "value": "{{sample_order_id}}"}]}, "description": "Get order items after token validation"}, "response": []}, {"name": "Cancel Order (Token Validation)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{app_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"customer_id\": \"{{customer_id}}\",\n  \"order_id\": \"{{sample_order_id}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/cancel_order", "host": ["{{baseUrl}}"], "path": ["api", "v1", "cancel_order"]}, "description": "Cancel order after token validation"}, "response": []}, {"name": "Return Items (Token Validation)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{app_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"customer_id\": \"{{customer_id}}\",\n  \"order_id\": \"{{sample_order_id}}\",\n  \"items\": [\n    {\n      \"sku\": \"ROZ13792-40-COTTONBLEND-CORE-1-DAILYWEAR\",\n      \"quantity\": 1\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/return_items", "host": ["{{baseUrl}}"], "path": ["api", "v1", "return_items"]}, "description": "Return items after token validation"}, "response": []}, {"name": "Return Full Order (Token Validation)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{app_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"customer_id\": \"{{customer_id}}\",\n  \"order_id\": \"{{sample_order_id}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/return_full_order", "host": ["{{baseUrl}}"], "path": ["api", "v1", "return_full_order"]}, "description": "Return full order after token validation"}, "response": []}, {"name": "get orders by phonenumber", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{api_token}}"}], "url": {"raw": "{{baseUrl}}/api/v1/get_orders_by_phone_number?page_size=20&page=1&sort_order=desc&phone_number=9123456789", "host": ["{{baseUrl}}"], "path": ["api", "v1", "get_orders_by_phone_number"], "query": [{"key": "page_size", "value": "20"}, {"key": "page", "value": "1"}, {"key": "sort_order", "value": "desc"}, {"key": "search", "value": "Z3", "disabled": true}, {"key": "phone_number", "value": "9123456789"}]}}, "response": []}, {"name": "Get Refund Details by Phone", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{api_token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/v1/refund_details_by_phone?phone_number=9123456789", "host": ["{{baseUrl}}"], "path": ["api", "v1", "refund_details_by_phone"], "query": [{"key": "phone_number", "value": "9123456789"}]}, "description": "Get refund details for orders by phone number"}, "response": []}, {"name": "Create Return", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{api_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"order_id\": \"{{sample_order_id}}\",\n  \"return_reason_code\": \"DEFECTIVE\",\n  \"comments\": \"Product was defective\",\n  \"order_full_return\": false,\n  \"items\": [\n    {\n      \"sku\": \"ROZ1602-1PCS\",\n      \"quantity\": 1\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/create_return", "host": ["{{baseUrl}}"], "path": ["api", "v1", "create_return"]}, "description": "Create a return request with token validation"}, "response": []}], "description": "These endpoints require external token validation service"}, {"name": "API Payment Endpoints (Token Validation)", "item": [{"name": "Create Payment Order (API)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{api_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"order_id\": \"{{sample_order_id}}\",\n  \"amount\": 299.99,\n  \"customer_id\": \"{{customer_id}}\",\n  \"customer_name\": \"<PERSON>\",\n  \"customer_email\": \"<EMAIL>\",\n  \"customer_phone\": \"+91-9876543210\",\n  \"notes\": {\n    \"description\": \"Payment for order {{sample_order_id}}\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/create_payment_order", "host": ["{{baseUrl}}"], "path": ["api", "v1", "create_payment_order"]}, "description": "Create a Razorpay payment order via API with token validation"}, "response": []}, {"name": "Verify Payment (API)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{api_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"oms_order_id\": \"{{sample_order_id}}\",\n  \"razorpay_order_id\": \"order_sample123\",\n  \"razorpay_payment_id\": \"pay_sample123\",\n  \"razorpay_signature\": \"sample_signature_hash\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/verify_payment", "host": ["{{baseUrl}}"], "path": ["api", "v1", "verify_payment"]}, "description": "Verify payment signature and update order status via API"}, "response": []}, {"name": "Get Payment Status (API)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{api_token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/v1/payment_status/{{sample_order_id}}", "host": ["{{baseUrl}}"], "path": ["api", "v1", "payment_status", "{{sample_order_id}}"]}, "description": "Get payment status for a specific order via API"}, "response": []}], "description": "Payment-related API endpoints with token validation"}, {"name": "Webhook APIs", "item": [{"name": "Razorpay Webhook", "request": {"method": "POST", "header": [{"key": "X-Razorpay-Signature", "value": "sample_webhook_signature", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"event\": \"payment.captured\",\n  \"payload\": {\n    \"payment\": {\n      \"entity\": {\n        \"id\": \"pay_sample123\",\n        \"amount\": 29999,\n        \"status\": \"captured\",\n        \"notes\": {\n          \"oms_order_id\": \"{{sample_order_id}}\"\n        }\n      }\n    }\n  }\n}"}, "url": {"raw": "{{baseUrl}}/webhooks/v1/razorpay_webhook", "host": ["{{baseUrl}}"], "path": ["webhooks", "v1", "razorpay_webhook"]}, "description": "Handle Razorpay webhook notifications for payment status updates"}, "response": []}]}, {"name": "Authentication", "item": [{"name": "auth-app", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"grant_type\": \"refresh_token\",\n    \"refresh_token\": \"AMf-vBwuDcyi3Uyhhx1u9QuEJtnnRgcMC3x06S9WRFSVB8R3DLZDP36-ezs01zEa34pg7wdRC6D_1N2-mmdKwx6Hm53te4-yLPBQhwo0cPZPje6IPRj96DEFxcjVP8NhN982COAvQc7YjCNkFIxHgky2rkRxvrGWlX5GDPdAyc43aH6o2OFkgKwyFt08dq1XrJrepQGevu0h27Cg8rZJJsuXtuRGFCHeJeoSn4stYT9BBSfMELG1s5U\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://securetoken.googleapis.com/v1/token?key=AIzaSyBEEWROSUbSLVl9T3HHSp8VdgsU6oTrdFI", "protocol": "https", "host": ["securetoken", "googlea<PERSON>", "com"], "path": ["v1", "token"], "query": [{"key": "key", "value": "AIzaSyBEEWROSUbSLVl9T3HHSp8VdgsU6oTrdFI"}]}, "description": "Get Firebase App authentication token for mobile app requests"}, "response": []}, {"name": "auth-pos", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"wZVWpnSIpY\",\n    \"returnSecureToken\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=AIzaSyC_fE1A0JapukDalgCdJix4mmoVWx-K-2g", "protocol": "https", "host": ["identitytoolkit", "googlea<PERSON>", "com"], "path": ["v1", "accounts:signInWithPassword"], "query": [{"key": "key", "value": "AIzaSyC_fE1A0JapukDalgCdJix4mmoVWx-K-2g"}]}, "description": "Get Firebase POS authentication token for POS system requests"}, "response": []}, {"name": "0_Token-ClientId,SecretId", "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "grant_type", "value": "client_credentials", "type": "text"}, {"key": "client_id", "value": "7DUH8zDwkI7ITn9JHYlzNRmrQTOdqCK4SBZlzDUB", "type": "text", "disabled": true}, {"key": "client_secret", "value": "Ldb4cDLRb2EA2snyHAdqLiSg96A42oL25IqRwYrqMxuUrVWpl9JQiLlK2nCvlGklKXa8PRY4ChnmVmVyVV0Dtj2hoN5tfKHeBKIfGQaKxumYXBKxOyb0CTHaPbRk8L95", "type": "text", "disabled": true}, {"key": "client_id", "value": "jJAyGAKkHJxJeMYsSVprCrssuYlBDVU0WQsVAxWf", "type": "text"}, {"key": "client_secret", "value": "sPD7eHBavsKjI5RZUPCggwBVs19Ge4n11WUcr8M7frdjPIjikdzodb2C5E8Ff7xwVPbqm6Uw9PPyOntFnf9ytIwr81UkmZk8KeptrOJ39EmiRO2j44f7nIgzUuBkATyG", "type": "text"}]}, "url": {"raw": "https://auth-uat.rozana.tech/o/token/", "protocol": "https", "host": ["auth-uat", "rozana", "tech"], "path": ["o", "token", ""]}}, "response": []}, {"name": "auth-api", "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "grant_type", "value": "client_credentials", "type": "text"}, {"key": "client_id", "value": "4dZRq3ARIcLhd4N2K6YdSbOZvysCJecJ69lozxCE", "type": "text"}, {"key": "client_secret", "value": "XWwvI3ywwQBON56p5N4FNS3YSwPCFhT7HCU4Cz0fxzAXlnnuoNIKk0sB3xKAK7YLfcAuHEL6BE4filitEV5BaWS0Qn3eI8ViAziQ3tT2QD5JAn37yYnn2RFoY1yYjboK", "type": "text"}]}, "url": {"raw": "http://localhost:8021/o/token/", "protocol": "http", "host": ["localhost"], "port": "8021", "path": ["o", "token", ""]}}, "response": []}, {"name": "wallet", "request": {"method": "POST", "header": [{"key": "sec-ch-ua-platform", "value": "\"Android\""}, {"key": "<PERSON><PERSON><PERSON>", "value": "https://rzn1.stockone.com/"}, {"key": "sec-ch-ua", "value": "\"Not;A=Brand\";v=\"99\", \"Google Chrome\";v=\"139\", \"Chromium\";v=\"139\""}, {"key": "sec-ch-ua-mobile", "value": "?1"}, {"key": "x-Geolocation", "value": "12.934302590149795,77.63010364970812"}, {"key": "User-Agent", "value": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}, {"key": "Accept", "value": "application/json, text/plain, */*"}, {"key": "x-api-key", "value": "your-secure-internal-api-key"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"related_id\": \"credit_001\",\n    \"order_id\": \"order_123\",\n    \"wallet_amt\": 500,\n    \"entry_type\": \"credit\",\n    \"description\": \"Addingd money to wallet\",\n    \"reference_type\": \"ansds=adsnsdjanajk\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://wallet-uat.rozana.tech/internal/wallet-entry/2CN3aYJnaGXpaguuctWAubZnKKp1/add-entry", "protocol": "https", "host": ["wallet-uat", "rozana", "tech"], "path": ["internal", "wallet-entry", "2CN3aYJnaGXpaguuctWAubZnKKp1", "add-entry"]}}, "response": []}, {"name": "validate token", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token 4gd1Ab0ikVGrJ9GLtHcFfnah6orC39"}], "url": {"raw": "http://localhost:8021/o/authorize/", "protocol": "http", "host": ["localhost"], "port": "8021", "path": ["o", "authorize", ""]}}, "response": []}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:8000", "type": "string"}, {"key": "app_token", "value": "", "type": "string", "description": "Firebase App authentication token (use raw token without Bearer prefix)"}, {"key": "pos_token", "value": "", "type": "string", "description": "Firebase POS authentication token (use raw token without Bearer prefix)"}, {"key": "customer_id", "value": "2CN3aYJnaGXpaguuctWAubZnKKp1", "type": "string"}, {"key": "sample_order_id", "value": "YDPM1", "type": "string"}, {"key": "api_token", "value": "", "type": "default"}, {"key": "auth_token", "value": "", "type": "default"}, {"key": "facility_name", "value": "ROZANA_TEST_WH1", "type": "default"}, {"key": "cust_988_id", "value": "", "type": "default"}]}