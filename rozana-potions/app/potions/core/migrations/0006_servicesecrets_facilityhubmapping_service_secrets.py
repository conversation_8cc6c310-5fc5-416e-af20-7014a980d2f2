# Generated by Django 5.2.5 on 2025-09-14 02:46

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0005_facilityhubmapping'),
    ]

    operations = [
        migrations.CreateModel(
            name='ServiceSecrets',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('provider', models.CharField(choices=[('tms', 'TMS'), ('wms', 'WMS')], max_length=20)),
                ('service_key_type', models.CharField(choices=[('tms', 'TMS'), ('ondemand', 'OnDemand')], max_length=20)),
                ('service_key', models.Char<PERSON>ield(max_length=150)),
                ('active', models.<PERSON><PERSON>anField(default=True)),
            ],
            options={
                'db_table': 'SERVICE_SECRETS',
                'ordering': ['provider', 'service_key_type'],
                'unique_together': {('provider', 'service_key_type')},
            },
        ),
        migrations.AddField(
            model_name='facilityhubmapping',
            name='service_secrets',
            field=models.ForeignKey(blank=True, help_text='API keys for this hub', null=True, on_delete=django.db.models.deletion.CASCADE, to='core.servicesecrets'),
        ),
    ]
