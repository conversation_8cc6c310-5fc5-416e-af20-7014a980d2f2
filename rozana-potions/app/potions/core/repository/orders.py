from typing import Dict, Optional, List

# Logger
from potions.logging.utils import get_app_logger
logger = get_app_logger('order_repository')

# Database wrapper
from core.repository.main import OMSDatabase

class OrderRepository:
    """Repository for order operations on OMS database"""

    def __init__(self):
        self.db = OMSDatabase()

    def get_order_by_id(self, order_id: str) -> Optional[Dict]:
        """Get order by external order_id"""
        query = """
            SELECT * FROM orders
            WHERE order_id = %s
        """
        return self.db.fetch_one(query, (order_id,))

    def update_order_status(self, order_id: str, new_status: int) -> bool:
        """Update order status"""
        query = """
            UPDATE orders 
            SET status = %(new_status)s, updated_at = NOW()
            WHERE order_id = %(order_id)s
        """
        affected_rows = self.db.execute_update(query, {
            'order_id': order_id,
            'new_status': new_status
        })
        return affected_rows > 0

    def update_all_order_items_status(self, order_id: str, new_status: int) -> int:
        """Update status for all order_items belonging to the external order_id.

        Returns number of affected rows.
        """
        query = """
            UPDATE order_items 
            SET status = %(new_status)s,
                updated_at = NOW()
            WHERE order_id = (SELECT id FROM orders WHERE order_id = %(order_id)s)
        """
        params = {
            'order_id': order_id,
            'new_status': new_status,
        }
        try:
            return self.db.execute_update(query, params)
        except Exception as e:
            logger.error(f"Failed to update order_items for order_id={order_id} to status={new_status}: {e}", exc_info=True)
            return 0

    def get_customer_id_by_order_id(self, order_id: str) -> Optional[str]:
        """Get customer id by order_id"""
        query = """
            SELECT customer_id FROM orders
            WHERE order_id = %s
        """
        result = self.db.fetch_one(query, (order_id,))
        return result.get('customer_id') if result else None

    def get_order_item_by_wh_sku_and_pack_qty(self, order_id: str, sku_code: str, pack_uom_quantity: int) -> Optional[Dict]:
        """Get order item by order_id, sku_code and pack_uom_quantity"""
        query = """
            SELECT oi.* FROM order_items oi
            JOIN orders o ON oi.order_id = o.id
            WHERE o.order_id = %(order_id)s
            AND oi.wh_sku = %(sku_code)s
            AND oi.pack_uom_quantity = %(pack_uom_quantity)s
        """
        return self.db.fetch_one(query, {
            'order_id': order_id,
            'sku_code': sku_code,
            'pack_uom_quantity': pack_uom_quantity
        })

    def get_return_items_for_refund(self, order_id: str, return_reference: str):
        """
        Get return items that need refunding based on return status
        Only returns items with approved status that haven't been refunded yet
        """
        query = """
        SELECT 
            ri.id as return_item_id,
            ri.return_id,
            ri.order_item_id,
            ri.sku,
            ri.quantity_returned,
            ri.accepted_quantity,
            ri.unit_price,
            ri.sale_price,
            ri.refund_amount,
            ri.status as return_item_status,
            r.status as return_status,
            r.refund_status,
            r.return_reference,
            oi.id as order_item_id,
            oi.wh_sku as wh_sku
        FROM return_items ri
        JOIN returns r ON ri.return_id = r.id
        JOIN orders o ON r.order_id = o.id
        JOIN order_items oi ON ri.order_item_id = oi.id
        WHERE o.order_id = %(order_id)s 
          AND r.refund_status = 'pending'
          AND r.return_reference = %(return_reference)s
        """

        return self.db.execute_query(query, {'order_id': order_id, 'return_reference': return_reference})

    def get_return_items_by_order_id(self, order_id: str):
        """
        Get all return items for a specific order
        """
        query = """
        SELECT 
            ri.*,
            r.return_reference,
            r.status as return_status,
            r.refund_status
        FROM return_items ri
        JOIN returns r ON ri.return_id = r.id
        JOIN orders o ON r.order_id = o.id
        WHERE o.order_id = %(order_id)s
        ORDER BY ri.id
        """
        
        return self.db.execute_query(query, {'order_id': order_id})

    def update_return_refund_status(self, return_id: int, refund_status: str):
        """
        Update the refund_status in returns table after processing refunds
        """
        query = """
        UPDATE returns 
        SET refund_status = %(refund_status)s,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = %(return_id)s
        """
        
        return self.db.execute_update(query, {
            'return_id': return_id,
            'refund_status': refund_status
        })
            
    def get_return_refund_summary(self, order_id: int):
        """
        Get summary of returns and refunds for an order based on return status
        """
        query = """
        SELECT 
            COUNT(DISTINCT r.id) as total_returns,
            COUNT(ri.id) as total_return_items,
            SUM(ri.quantity_returned) as total_returned_quantity,
            SUM(CASE WHEN r.refund_status = 'completed' THEN ri.quantity_returned ELSE 0 END) as refunded_quantity,
            SUM(CASE WHEN r.refund_status = 'pending' THEN ri.quantity_returned ELSE 0 END) as pending_refund_quantity,
            SUM(ri.refund_amount) as total_refund_amount
        FROM returns r
        JOIN return_items ri ON r.id = ri.return_id
        WHERE r.order_id = %(order_id)s 
          AND r.status = 'approved'
          AND ri.status = 'approved'
        """
        
        result = self.db.fetch_one(query, {'order_id': order_id})
        return result if result else {}

    def get_order_items_with_cancelled_quantity(self, order_id: str) -> List[Dict]:
        """Get order items that have cancelled quantity > refunded quantity"""
        query = """
            SELECT oi.* FROM order_items oi
            JOIN orders o ON oi.order_id = o.id
            WHERE o.order_id = %(order_id)s
            AND oi.cancelled_quantity > oi.refunded_quantity
        """
        return self.db.execute_query(query, {'order_id': order_id})

    def get_order_items_with_unfulfilled_quantity(self, order_id: str) -> List[Dict]:
        """
        Get order items that have unfulfilled quantity requiring refund.
        Logic: Only items where unfulfilled_qty > refunded_qty (prevents double refunding)
        """
        query = """
            SELECT oi.* FROM order_items oi
            JOIN orders o ON oi.order_id = o.id
            WHERE o.order_id = %(order_id)s 
            AND COALESCE(oi.unfulfilled_quantity, 0) > 0
        """
        return self.db.execute_query(query, {'order_id': order_id})

    def get_all_order_items_by_order_id(self, order_id: str) -> List[Dict]:
        """Get all order items for an order"""
        query = """
            SELECT oi.* FROM order_items oi
            JOIN orders o ON oi.order_id = o.id
            WHERE o.order_id = %(order_id)s
        """
        return self.db.execute_query(query, {'order_id': order_id})

    def get_latest_payment_mode_by_order_pk(self, order_pk: int) -> Optional[str]:
        """
        Get the latest payment_mode from payment_details for a given orders.id (primary key).
        Returns the payment_mode string or None if not found.
        """
        query = """
            SELECT payment_mode
            FROM payment_details
            WHERE order_id = %(order_pk)s
            ORDER BY payment_date DESC
            LIMIT 1
        """
        try:
            row = self.db.fetch_one(query, {'order_pk': order_pk})
            if not row:
                return None
            return row.get('payment_mode') if isinstance(row, dict) else row[0]
        except Exception as e:
            logger.error(f"Failed to fetch latest payment_mode for order_pk={order_pk}: {e}", exc_info=True)
            return None

    def update_order_item_cancelled_quantity(self, item_id: int, cancelled_quantity: float, new_status: int = None) -> bool:
        """Update order item cancelled quantity and optionally status"""
        if new_status is not None:
            query = """
                UPDATE order_items 
                SET cancelled_quantity = %(cancelled_quantity)s, 
                    status = %(new_status)s,
                    updated_at = NOW()
                WHERE id = %(item_id)s
            """
            params = {
                'item_id': item_id,
                'cancelled_quantity': cancelled_quantity,
                'new_status': new_status
            }
        else:
            query = """
                UPDATE order_items 
                SET cancelled_quantity = %(cancelled_quantity)s,
                    updated_at = NOW()
                WHERE id = %(item_id)s
            """
            params = {
                'item_id': item_id,
                'cancelled_quantity': cancelled_quantity
            }

        affected_rows = self.db.execute_update(query, params)
        return affected_rows > 0

    def update_order_item_refunded_quantity(self, item_id: int, refunded_quantity: float) -> bool:
        """Update order item refunded quantity"""
        query = """
            UPDATE order_items 
            SET refunded_quantity = %(refunded_quantity)s,
                updated_at = NOW()
            WHERE id = %(item_id)s
        """
        params = {
            'item_id': item_id,
            'refunded_quantity': refunded_quantity
        }
        
        affected_rows = self.db.execute_update(query, params)
        return affected_rows > 0

    def increment_order_item_refunded_quantity(self, order_id: str, sku_code: str, increment_qty: float) -> bool:
        """
        Increment refunded_quantity for an order item identified by external order_id and SKU.

        Args:
            order_id: External order_id from orders.order_id
            sku_code: SKU code from order_items.sku
            increment_qty: Quantity to add to refunded_quantity

        Returns:
            True if at least one row was updated, else False
        """
        query = """
            UPDATE order_items oi
            SET refunded_quantity = COALESCE(oi.refunded_quantity, 0) + %(increment_qty)s,
                updated_at = NOW()
            FROM orders o
            WHERE oi.order_id = o.id
              AND o.order_id = %(order_id)s
              AND oi.sku = %(sku_code)s
        """
        params = {
            'order_id': order_id,
            'sku_code': sku_code,
            'increment_qty': increment_qty,
        }
        try:
            affected = self.db.execute_update(query, params)
            return (affected or 0) > 0
        except Exception as e:
            logger.error(f"Failed to increment refunded_quantity | order_id={order_id} sku={sku_code} inc={increment_qty}: {e}", exc_info=True)
            return False

    def get_order_with_address_and_items(self, order_id: str) -> Optional[Dict]:
        """Get order with delivery address and items for forward consignment"""
        query = """
            SELECT 
                o.order_id,
                o.facility_name,
                o.order_mode,
                oa.full_name,
                oa.phone_number,
                oa.address_line1,
                oa.address_line2,
                oa.city,
                oa.state,
                oa.postal_code,
                oa.country,
                oa.latitude,
                oa.longitude
            FROM orders o
            LEFT JOIN order_addresses oa ON oa.order_id = o.id
            WHERE o.order_id = %s
        """
        return self.db.fetch_one(query, (order_id,))

    def get_sale_price_by_sku(self, order_id: str, sku_code: str) -> Optional[float]:
        """Get sale_price for a specific SKU in an order"""
        query = """
            SELECT oi.sale_price
            FROM order_items oi
            JOIN orders o ON oi.order_id = o.id
            WHERE o.order_id = %s AND oi.sku = %s
            LIMIT 1
        """
        result = self.db.fetch_one(query, (order_id, sku_code))
        return float(result['sale_price']) if result and result.get('sale_price') else None

    def get_order_items_for_consignment(self, order_id: str) -> List[Dict]:
        """Get order items for forward consignment payload"""
        query = """
            SELECT
                oi.sku,
                oi.wh_sku,
                oi.name,
                oi.fulfilled_quantity,
                oi.sale_price,
                oi.unit_price
            FROM order_items oi
            JOIN orders o ON oi.order_id = o.id
            WHERE o.order_id = %s AND oi.fulfilled_quantity > 0
        """
        return self.db.execute_query(query, (order_id,))

    def update_order_item_unfulfilled_quantity(self, order_id: str, sku_code: str, unfulfilled_quantity: float) -> bool:
        """Update order item unfulfilled quantity by order_id and sku_code"""
        query = """
            UPDATE order_items 
            SET unfulfilled_quantity = %(unfulfilled_quantity)s,
                updated_at = NOW()
            WHERE order_id = (SELECT id FROM orders WHERE order_id = %(order_id)s)
            AND wh_sku = %(sku_code)s
        """
        params = {
            'order_id': order_id,
            'sku_code': sku_code,
            'unfulfilled_quantity': unfulfilled_quantity
        }

        affected_rows = self.db.execute_update(query, params)
        return affected_rows > 0

    def update_order_item_wh_sku_cancelled_quantity(self, order_id: str, sku_code: str, cancelled_quantity: float, new_status: int = None) -> bool:
        """Update order item cancelled quantity by order_id and sku_code"""

        # First, get the current cancelled quantity and internal order ID for logging
        current_query = """
            SELECT oi.cancelled_quantity, o.id as internal_order_id
            FROM order_items oi
            JOIN orders o ON oi.order_id = o.id
            WHERE o.order_id = %(order_id)s
            AND oi.wh_sku = %(sku_code)s
        """
        current_params = {
            'order_id': order_id,
            'sku_code': sku_code
        }

        current_result = self.db.execute_query(current_query, current_params)
        if not current_result:
            logger.warning(f"Order item not found for order_id: {order_id}, sku_code: {sku_code}")
            return False

        previous_cancelled_quantity = current_result[0]['cancelled_quantity']
        internal_order_id = current_result[0]['internal_order_id']

        # Log the change
        logger.info(f"Updating cancelled quantity for order_id: {order_id}, sku_code: {sku_code} - Previous: {previous_cancelled_quantity}, New: {cancelled_quantity}")

        if new_status is not None:
            query = """
                UPDATE order_items 
                SET cancelled_quantity = %(cancelled_quantity)s, 
                    status = %(new_status)s,
                    updated_at = NOW()
                WHERE order_id = %(internal_order_id)s
                AND wh_sku = %(sku_code)s
            """
            params = {
                'internal_order_id': internal_order_id,
                'sku_code': sku_code,
                'cancelled_quantity': cancelled_quantity,
                'new_status': new_status
            }
        else:
            query = """
                UPDATE order_items 
                SET cancelled_quantity = %(cancelled_quantity)s,
                    updated_at = NOW()
                WHERE order_id = %(internal_order_id)s
                AND wh_sku = %(sku_code)s
            """
            params = {
                'internal_order_id': internal_order_id,
                'sku_code': sku_code,
                'cancelled_quantity': cancelled_quantity
            }

        affected_rows = self.db.execute_update(query, params)
        return affected_rows > 0


    def update_order_item_quantities(self, order_id: str, sku_code: str, fulfilled_quantity: float, cancelled_quantity: float, unfulfilled_quantity: float, new_status: int = None, pack_uom_quantity: int = 1) -> bool:
        """Update multiple quantity fields for an order item in a single database call."""

        if new_status is not None:
            query = """
                UPDATE order_items
                SET fulfilled_quantity = %(fulfilled_quantity)s,
                    cancelled_quantity = %(cancelled_quantity)s,
                    unfulfilled_quantity = %(unfulfilled_quantity)s,
                    status = %(new_status)s,
                    updated_at = NOW()
                WHERE order_id = (SELECT id FROM orders WHERE order_id = %(order_id)s)
                AND wh_sku = %(sku_code)s and pack_uom_quantity = %(pack_uom_quantity)s
            """
        else:
            query = """
                UPDATE order_items
                SET fulfilled_quantity = %(fulfilled_quantity)s,
                    cancelled_quantity = %(cancelled_quantity)s,
                    unfulfilled_quantity = %(unfulfilled_quantity)s,
                    updated_at = NOW()
                WHERE order_id = (SELECT id FROM orders WHERE order_id = %(order_id)s)
                AND wh_sku = %(sku_code)s and pack_uom_quantity = %(pack_uom_quantity)s
            """

        params = {
            'order_id': order_id,
            'sku_code': sku_code,
            'fulfilled_quantity': fulfilled_quantity,
            'cancelled_quantity': cancelled_quantity,
            'unfulfilled_quantity': unfulfilled_quantity,
            'new_status': new_status,
            'pack_uom_quantity': pack_uom_quantity
        }

        try:
            affected_rows = self.db.execute_update(query, params)
            return affected_rows > 0
        except Exception as e:
            logger.error(f"Failed to update order item quantities | order_id={order_id} | sku={sku_code} | error={str(e)}", exc_info=True)
            return False

    def get_internal_order_id(self, order_id: str) -> Optional[int]:
        """Get internal order ID by external order_id"""
        query = "SELECT id FROM orders WHERE order_id = %s"
        result = self.db.fetch_one(query, (order_id,))
        return result['id'] if result else None

    def get_order_item_refunded_quantity(self, order_id: str, item_id: int) -> float:
        """Get current refunded quantity for an order item"""
        query = """
            SELECT COALESCE(oi.refunded_quantity, 0) as refunded_quantity
            FROM order_items oi
            JOIN orders o ON oi.order_id = o.id
            WHERE o.order_id = %(order_id)s AND oi.id = %(item_id)s
        """
        result = self.db.fetch_one(query, {
            'order_id': order_id,
            'item_id': item_id
        })
        return float(result.get('refunded_quantity', 0)) if result else 0.0

order_repository = OrderRepository()
