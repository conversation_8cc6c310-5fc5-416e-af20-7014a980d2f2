from typing import Dict, Optional

# Logger
from potions.logging.utils import get_app_logger
logger = get_app_logger('invoice_details_repository')

# Database wrapper
from core.repository.main import OMSDatabase

class InvoiceDetailsRepository:
    """Repository for invoice_details table operations"""

    def __init__(self):
        self.db = OMSDatabase()

    def get_by_order_id(self, internal_order_id: int) -> Optional[Dict]:
        """Get invoice details by internal order ID"""
        query = "SELECT * FROM invoice_details WHERE order_id = %s"
        return self.db.fetch_one(query, (internal_order_id,))

    def create_invoice_details(self, invoice_number: str, internal_order_id: int) -> bool:
        """Create new invoice details record"""
        query = """
            INSERT INTO invoice_details (invoice_number, order_id, created_at, updated_at) 
            VALUES (%s, %s, NOW(), NOW())
        """
        try:
            affected_rows = self.db.execute_update(query, (invoice_number, internal_order_id))
            return affected_rows > 0
        except Exception as e:
            logger.error(f"Failed to create invoice details | invoice_number={invoice_number} | order_id={internal_order_id} | error={str(e)}", exc_info=True)
            return False

    def update_invoice_details(self, internal_order_id: int, invoice_number: str, invoice_s3_url: str = None, raven_link: str = None) -> bool:
        """Update existing invoice details"""
        query = """
            UPDATE invoice_details 
            SET invoice_number = %s, updated_at = NOW()
        """
        params = [invoice_number]
        
        if invoice_s3_url is not None:
            query += ", invoice_s3_url = %s"
            params.append(invoice_s3_url)
            
        if raven_link is not None:
            query += ", raven_link = %s"
            params.append(raven_link)
            
        query += " WHERE order_id = %s"
        params.append(internal_order_id)
        
        try:
            affected_rows = self.db.execute_update(query, tuple(params))
            return affected_rows > 0
        except Exception as e:
            logger.error(f"Failed to update invoice details | order_id={internal_order_id} | error={str(e)}", exc_info=True)
            return False

invoice_details_repository = InvoiceDetailsRepository()
