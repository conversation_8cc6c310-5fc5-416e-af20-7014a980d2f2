import json
import random
import string
from datetime import datetime
from zoneinfo import ZoneInfo
from typing import Dict, Any, List, Optional

# Logger
from potions.logging.utils import get_app_logger

# Database wrapper
from core.repository.main import OMSDatabase
from core.repository.orders import order_repository

# Initialize logger
logger = get_app_logger('sales_return_repository')


class SalesReturnRepository:
    """Repository for sales return operations on OMS database"""

    def __init__(self):
        self.db = OMSDatabase()

    def get_return_with_order_by_reference(self, return_reference: str) -> Optional[Dict[str, Any]]:
        """
        Fetch a sales return joined with order details by return_reference.
        Mirrors the previous SQL used in SalesReturnService.
        
        Returns:
            Dict containing return and order details, or None if not found
        """
        query = """
            SELECT r.id, r.return_reference, r.order_id, r.return_type, r.return_reason,
                   r.total_refund_amount, r.refund_status, r.status, r.created_at, r.updated_at,
                   o.order_id, o.facility_id, o.facility_name, o.customer_id
            FROM returns r
            JOIN orders o ON r.order_id = o.id
            WHERE r.return_reference = %s
        """
        results = self.db.execute_query(query, (return_reference,))
        return results[0] if results else None

    def get_order_id_by_return_reference(self, return_reference: str) -> Optional[str]:

        """
        Get order_id from return_reference for refund processing.
        
        Args:
            return_reference: The return reference (e.g., RTN-25090910-JA1D)
            
        Returns:
            order_id string or None if not found
        """
        try:
            query = """
                SELECT o.order_id 
                FROM returns r
                JOIN orders o ON r.order_id = o.id
                WHERE r.return_reference = %s
                LIMIT 1
            """
            results = self.db.execute_query(query, (return_reference,))
            if results:
                return results[0]['order_id']
            return None
        except Exception as e:
            logger.error(f"Error getting order_id for return_reference {return_reference}: {e}", exc_info=True)
            return None

    def get_return_items_by_reference(self, return_reference: str) -> List[Dict[str, Any]]:
        """
        Fetch sales return items for a given return_reference.
        Column set mirrors the previous query to keep service logic unchanged.
        """
        query = """
            SELECT ri.sku,
                   oi.wh_sku,
                   ri.quantity_returned,
                   ri.unit_price,
                   ri.sale_price,
                   ri.refund_amount,
                   ri.return_reason,
                   ri.item_condition,
                   ri.status,
                   oi.sgst,
                   oi.cgst,
                   oi.cess,
                   oi.igst,
                   ri.order_item_id
            FROM return_items ri
            JOIN order_items oi ON ri.order_item_id = oi.id
            JOIN returns r ON ri.return_id = r.id
            WHERE r.return_reference = %s
        """
        return self.db.execute_query(query, (return_reference,))

    def update_return_status(self, return_reference: str, status: str) -> int:
        """
        Update sales return status by return_reference.
        
        Args:
            return_reference: The unique reference ID of the return
            status: New status to set for the return
            
        Returns:
            int: Number of affected rows (0 if no return found with the reference)
            
        Raises:
            Exception: If database update fails
        """
        query = """
            UPDATE returns 
            SET status = %(status)s, updated_at = NOW()
            WHERE return_reference = %(return_reference)s
        """
        params = {
            'return_reference': return_reference,
            'status': status
        }
        try:
            logger.debug(f"Updating return status: reference={return_reference}, new_status={status}")
            result = self.db.execute_update(query, params)
            
            if result == 0:
                logger.warning(f"No return found with reference {return_reference} to update status")
            else:
                logger.info(f"Successfully updated status for return {return_reference} to {status}")
                
            return result
            
        except Exception as e:
            error_msg = f"Failed to update status for return {return_reference}: {str(e)}"
            logger.error(error_msg, exc_info=True)
            raise Exception(error_msg) from e

    def update_return_refund_status(self, return_reference: str, refund_status: str) -> int:
        """
        Update refund_status in returns table by return_reference.

        Args:
            return_reference: The unique reference ID of the return
            refund_status: New refund status to set (e.g., 'REFUNDED', 'completed', 'partial')

        Returns:
            int: Number of affected rows (0 if no return found with the reference)
        """
        query = """
            UPDATE returns
            SET refund_status = %(refund_status)s,
                updated_at = NOW()
            WHERE return_reference = %(return_reference)s
        """
        params = {
            'return_reference': return_reference,
            'refund_status': refund_status,
        }
        try:
            logger.debug(f"Updating return refund_status: reference={return_reference}, new_refund_status={refund_status}")
            result = self.db.execute_update(query, params)
            if result == 0:
                logger.warning(f"No return found with reference {return_reference} to update refund_status")
            else:
                logger.info(f"Successfully updated refund_status for return {return_reference} to {refund_status}")
            return result
        except Exception as e:
            error_msg = f"Failed to update refund_status for return {return_reference}: {str(e)}"
            logger.error(error_msg, exc_info=True)
            raise Exception(error_msg) from e

    def get_latest_return_reference_and_facility_by_order_id(self, order_id: str) -> Optional[Dict[str, Any]]:
        """
        Get the latest return_reference and facility_name for a given external order_id.
        Mirrors the join used in order_status_update for RTO delivered flow.

        Args:
            order_id: External order identifier (orders.order_id)

        Returns:
            Dict with keys {'return_reference', 'facility_name'} or None if not found
        """
        query = """
            SELECT r.return_reference, o.facility_name
            FROM returns r
            JOIN orders o ON r.order_id = o.id
            WHERE o.order_id = %s
            ORDER BY r.created_at DESC
            LIMIT 1
        """
        row = self.db.fetch_one(query, (order_id,))
        return row if row else None

    def update_return_items_status(self, return_reference: str, status_value: str, skus: Optional[List[str]] = None) -> int:
        """
        Minimal: mark all items for the given return_reference with the provided status.
        SKU filtering is intentionally ignored to keep logic simple.

        Returns the number of affected rows.
        """
        query = """
            UPDATE return_items ri
            SET status = %(status)s,
                updated_at = NOW()
            FROM returns r
            WHERE ri.return_id = r.id
              AND r.return_reference = %(return_reference)s
        """

        params: Dict[str, Any] = {
            'status': status_value,
            'return_reference': return_reference,
        }

        try:
            return self.db.execute_update(query, params)
        except Exception as e:
            error_msg = f"Failed to update return_items for reference {return_reference}: {str(e)}"
            logger.error(error_msg, exc_info=True)
            raise Exception(error_msg) from e

    def get_return_items_by_wh_sku_and_pack_uom(self, return_reference: str, sku_code: str, pack_uom_quantity: str) -> List[Dict[str, Any]]:
        """
        Fetch return items based on sku_code, pack_uom_quantity, and return_reference.
        
        Args:
            return_reference: The unique reference ID of the return
            sku_code: SKU code to match with order_items.wh_sku
            pack_uom_quantity: Pack UOM quantity to match with order_items.pack_uom_quantity
            
        Returns:
            List[Dict]: List of return items with their details
            
        Raises:
            Exception: If database query fails
        """
        query = """
            SELECT ri.id as return_item_id,
                   ri.sku,
                   ri.quantity_returned,
                   ri.accepted_quantity,
                   ri.unit_price,
                   ri.sale_price,
                   ri.refund_amount,
                   ri.return_reason,
                   ri.item_condition,
                   ri.status,
                   ri.order_item_id,
                   oi.wh_sku,
                   oi.pack_uom_quantity,
                   r.return_reference
            FROM return_items ri
            JOIN returns r ON ri.return_id = r.id
            JOIN order_items oi ON ri.order_item_id = oi.id
            WHERE r.return_reference = %(return_reference)s
              AND oi.wh_sku = %(sku_code)s
              AND oi.pack_uom_quantity = %(pack_uom_quantity)s
        """
        
        params = {
            'return_reference': return_reference,
            'sku_code': sku_code,
            'pack_uom_quantity': pack_uom_quantity
        }
        
        try:
            logger.debug(f"Fetching return items: reference={return_reference}, sku_code={sku_code}, "
                       f"pack_uom_quantity={pack_uom_quantity}")
            
            results = self.db.execute_query(query, params)
            
            logger.info(f"Found {len(results)} return item(s) for sku_code={sku_code}, "
                      f"pack_uom_quantity={pack_uom_quantity} in return {return_reference}")
            
            return results
            
        except Exception as e:
            error_msg = f"Failed to fetch return items for return {return_reference}: {str(e)}"
            logger.error(error_msg, exc_info=True)
            raise Exception(error_msg) from e

    def update_return_item_by_id(self, return_item_id: int, accepted_quantity: float, status: str) -> int:
        """
        Update a specific return item by its internal ID with accepted quantity and status.
        
        Args:
            return_item_id: The internal ID of the return item
            accepted_quantity: The accepted quantity for this return item
            status: New status to set for the return item
            
        Returns:
            int: Number of affected rows (should be 1 if successful, 0 if not found)
            
        Raises:
            Exception: If database update fails
        """
        query = """
            UPDATE return_items
            SET accepted_quantity = %(accepted_quantity)s,
                status = %(status)s,
                updated_at = NOW()
            WHERE id = %(return_item_id)s
        """
        
        params = {
            'return_item_id': return_item_id,
            'accepted_quantity': accepted_quantity,
            'status': status
        }
        
        try:
            logger.debug(f"Updating return item: id={return_item_id}, accepted_quantity={accepted_quantity}, status={status}")
            
            rows_updated = self.db.execute_update(query, params)
            
            if rows_updated == 0:
                logger.warning(f"No return item found with id={return_item_id}")
            else:
                logger.info(f"Successfully updated return item id={return_item_id} with accepted_quantity={accepted_quantity}, status={status}")
            
            return rows_updated
            
        except Exception as e:
            error_msg = f"Failed to update return item id={return_item_id}: {str(e)}"
            logger.error(error_msg, exc_info=True)
            raise Exception(error_msg) from e

    def create_rto_return_for_order(self, order_id_ext: str) -> Optional[str]:
        """
        Create RTO return record when none exists for RTO scenarios.
        Returns the created return_reference or None if failed.
        """
        try:
            # Get order details via repository (avoid runtime SELECT in INSERT)
            logger.info(f"[sales_return] Fetching order for return creation | order_id_ext={order_id_ext}")
            order_row = order_repository.get_order_by_id(order_id_ext)
            if not order_row:
                logger.warning(f"[sales_return] No order found for order_id_ext={order_id_ext}; skipping return creation")
                return None

            order_pk = order_row.get('id')
            facility_name = order_row.get('facility_name')
            customer_id = order_row.get('customer_id')
            total_amount = order_row.get('total_amount') or 0

            # Determine refund amount based on payment mode
            # If payment_mode is COD -> refund amount should be 0; else total_amount
            payment_mode = order_repository.get_latest_payment_mode_by_order_pk(order_pk)

            is_cod = str(payment_mode).lower() == 'cod'
            header_refund_amount = 0 if is_cod else total_amount

            # Generate RTO return reference in format: RTO-YYMMDDHH-RAND4 using IST time
            now = datetime.now(ZoneInfo('Asia/Kolkata'))
            rand = ''.join(random.choices(string.ascii_uppercase + string.digits, k=4))
            return_reference = f"RTO-{now.strftime('%y%m%d%H')}-{rand}"

            # Create return header with explicit reason 'customer_not_available'
            logger.info(f"[sales_return] Inserting return header | order_pk={order_pk} return_reference={return_reference}")
            insert_result = self.db.fetch_one(
                """
                INSERT INTO returns (
                    return_reference, order_id, customer_id, return_type, return_reason,
                    return_method, total_refund_amount, refund_status, status, created_at, updated_at
                ) VALUES (
                    %(return_reference)s, %(order_id)s, %(customer_id)s, 'RTO', 'customer_not_available',
                    'pickup', %(total_refund_amount)s, 'pending', 'approved', NOW(), NOW()
                )
                RETURNING id
                """,
                {
                    'return_reference': return_reference,
                    'order_id': order_pk,
                    'customer_id': customer_id,
                    'total_refund_amount': header_refund_amount,
                }
            )
            if not insert_result:
                logger.error(f"[sales_return] Return header insert returned no row | order_pk={order_pk} return_reference={return_reference}")
                return None

            return_id = insert_result.get('id') if isinstance(insert_result, dict) else insert_result[0]
            logger.info(f"[sales_return] Created return header | return_id={return_id} return_reference={return_reference}")

            # Create return items from order items using repository (no runtime SELECT in INSERT)
            # Note: set return_items.return_reason to 'customer_not_available'
            order_items = order_repository.get_all_order_items_by_order_id(order_id_ext) or []
            inserted_count = 0
            for oi in order_items:
                qty = oi.get('fulfilled_quantity') or 0
                if qty <= 0:
                    continue
                params = {
                    'return_id': return_id,
                    'order_item_id': oi.get('id'),
                    'sku': oi.get('sku'),
                    'quantity_returned': qty,
                    'unit_price': oi.get('unit_price') or 0,
                    'sale_price': oi.get('sale_price') or 0,
                    'refund_amount': (oi.get('sale_price') or 0) * qty,
                    'return_reason': 'customer_not_available',
                    'item_condition': 'good',
                    'status': 'approved',
                }
                rows = self.db.execute_update(
                    """
                    INSERT INTO return_items (
                        return_id, order_item_id, sku, quantity_returned,
                        unit_price, sale_price, refund_amount, return_reason,
                        item_condition, status, created_at, updated_at
                    ) VALUES (
                        %(return_id)s, %(order_item_id)s, %(sku)s, %(quantity_returned)s,
                        %(unit_price)s, %(sale_price)s, %(refund_amount)s, %(return_reason)s,
                        %(item_condition)s, %(status)s, NOW(), NOW()
                    )
                    """,
                    params
                )
                inserted_count += rows or 0
            logger.info(f"[sales_return] Inserted return items | count={inserted_count} return_id={return_id}")

            logger.info(f"Created RTO return {return_reference} for order {order_id_ext}")
            return return_reference

        except Exception as e:
            logger.error(f"Failed to create RTO return for order {order_id_ext}: {str(e)}", exc_info=True)
            return None


sales_return_repository = SalesReturnRepository()
