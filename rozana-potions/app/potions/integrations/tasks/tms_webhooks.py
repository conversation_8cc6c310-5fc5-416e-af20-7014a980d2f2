"""
TMS Webhook Processing Tasks

This module contains Celery tasks for processing TMS webhook events
and updating order statuses in the OMS database asynchronously.
"""

from celery import shared_task
from typing import Dict, Any
from integrations.services.oms.order_status_update import OMSOrderStatusUpdateService, process_webhook_payload
from integrations.models import TMSWebhookEvent

# Logger
from potions.logging.utils import get_app_logger
logger = get_app_logger('tms_webhooks')


@shared_task(bind=True, max_retries=0, default_retry_delay=60)
def process_tms_webhook_order_status(self, webhook_payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    Process TMS webhook payload and update order status in OMS database.
    
    This task is designed to be run asynchronously to avoid blocking the webhook endpoint.
    It includes retry logic for handling temporary failures.
    
    Args:
        webhook_payload: The webhook payload data to process
        
    Returns:
        dict: Processing result with success status and details
    """
    try:
        # Extract webhook details from payload
        webhook_type = webhook_payload.get('type', 'unknown')
        reference_number = webhook_payload.get('reference_number')
        
        logger.info(f"Processing TMS webhook payload: {webhook_type} - {reference_number}")
        
        # Process the complete webhook payload for comprehensive updates
        result = process_webhook_payload(webhook_payload)
        
        if result['success']:
            logger.info(f"Successfully processed webhook {webhook_type} - {reference_number} and updated OMS order status: {result}")
            return {
                'success': True,
                'webhook_type': webhook_type,
                'reference_number': reference_number,
                'oms_update_result': result
            }
        else:
            logger.warning(f"Failed to update OMS order status for webhook {webhook_type} - {reference_number}: {result}")
            
            # If it's a "not found" error, don't retry
            if 'not found' in result.get('error', '').lower():
                return {
                    'success': False,
                    'webhook_type': webhook_type,
                    'reference_number': reference_number,
                    'error': result.get('error'),
                    'retry_attempted': False
                }
            
            # For other errors, retry
            raise Exception(f"OMS update failed: {result.get('error')}")
            
    except Exception as exc:
        webhook_type = webhook_payload.get('type', 'unknown') if isinstance(webhook_payload, dict) else 'unknown'
        reference_number = webhook_payload.get('reference_number') if isinstance(webhook_payload, dict) else 'unknown'
        
        logger.error(f"Error processing TMS webhook {webhook_type} - {reference_number}: {str(exc)}")
        
        # Retry logic
        if self.request.retries < self.max_retries:
            logger.info(f"Retrying webhook processing for {webhook_type} - {reference_number} (attempt {self.request.retries + 1})")
            raise self.retry(exc=exc)
        else:
            logger.error(f"Max retries exceeded for webhook {webhook_type} - {reference_number}")
            return {
                'success': False,
                'webhook_type': webhook_type,
                'reference_number': reference_number,
                'error': f'Max retries exceeded: {str(exc)}',
                'retries': self.request.retries
            }


@shared_task(bind=True, max_retries=2, default_retry_delay=120)
def bulk_process_tms_webhooks(self, webhook_event_ids: list) -> Dict[str, Any]:
    """
    Bulk process multiple TMS webhook events.
    
    Args:
        webhook_event_ids: List of TMSWebhookEvent IDs to process
        
    Returns:
        dict: Bulk processing result with success/failure counts
    """
    try:
        results = {
            'success': True,
            'total_processed': len(webhook_event_ids),
            'successful_updates': 0,
            'failed_updates': 0,
            'details': []
        }
        
        logger.info(f"Starting bulk processing of {len(webhook_event_ids)} TMS webhook events")
        
        for webhook_event_id in webhook_event_ids:
            try:
                result = process_tms_webhook_order_status.apply(args=[webhook_event_id])
                
                if result.result.get('success'):
                    results['successful_updates'] += 1
                else:
                    results['failed_updates'] += 1
                    results['success'] = False
                
                results['details'].append({
                    'webhook_event_id': webhook_event_id,
                    'result': result.result
                })
                
            except Exception as e:
                logger.error(f"Error processing webhook {webhook_event_id} in bulk: {str(e)}")
                results['failed_updates'] += 1
                results['success'] = False
                results['details'].append({
                    'webhook_event_id': webhook_event_id,
                    'result': {
                        'success': False,
                        'error': str(e)
                    }
                })
        
        logger.info(f"Bulk processing completed: {results['successful_updates']} successful, "
                   f"{results['failed_updates']} failed out of {results['total_processed']} total")
        
        return results
        
    except Exception as exc:
        logger.error(f"Error in bulk processing TMS webhooks: {str(exc)}")
        
        if self.request.retries < self.max_retries:
            logger.info(f"Retrying bulk processing (attempt {self.request.retries + 1})")
            raise self.retry(exc=exc)
        else:
            return {
                'success': False,
                'error': f'Bulk processing failed after retries: {str(exc)}',
                'total_processed': len(webhook_event_ids),
                'successful_updates': 0,
                'failed_updates': len(webhook_event_ids)
            }


@shared_task
def cleanup_old_webhook_events(days_old: int = 30) -> Dict[str, Any]:
    """
    Clean up old TMS webhook events to prevent database bloat.
    
    Args:
        days_old: Number of days old events to keep (default: 30)
        
    Returns:
        dict: Cleanup result with count of deleted events
    """
    try:
        from django.utils import timezone
        from datetime import timedelta
        
        cutoff_date = timezone.now() - timedelta(days=days_old)
        
        # Delete old webhook events
        deleted_count, _ = TMSWebhookEvent.objects.filter(
            created_at__lt=cutoff_date
        ).delete()
        
        logger.info(f"Cleaned up {deleted_count} TMS webhook events older than {days_old} days")
        
        return {
            'success': True,
            'deleted_count': deleted_count,
            'cutoff_date': cutoff_date.isoformat(),
            'days_old': days_old
        }
        
    except Exception as e:
        logger.error(f"Error cleaning up old webhook events: {str(e)}")
        return {
            'success': False,
            'error': str(e),
            'deleted_count': 0
        }


@shared_task
def retry_failed_webhook_processing(hours_back: int = 24) -> Dict[str, Any]:
    
    try:
        from django.utils import timezone
        from datetime import timedelta
        
        cutoff_time = timezone.now() - timedelta(hours=hours_back)
        
        logger.info(f"Retry functionality disabled - webhook events are no longer stored in database")
        logger.info(f"Webhook events are now processed directly without persistence for retry")
        
        return {
            'success': True,
            'message': 'Retry functionality disabled - events are processed directly without database storage',
            'processed_count': 0
        }
        
    except Exception as e:
        logger.error(f"Error initiating retry processing: {str(e)}")
        return {
            'success': False,
            'error': str(e),
            'processed_count': 0
        }
