import asyncio
import redis
from celery import shared_task
from django.conf import settings

from integrations.services.liink_service import LiinkService
from integrations.services.wms.order_service import WMSOrderService
from integrations.services.wms.inventory_adjustment_service import (
    WMSInventoryAdjustmentService,
)

# Logger
from potions.logging.utils import get_app_logger
logger = get_app_logger('wms_orders')

# event publisher
from events.publisher import publish_event

# Service
from payments.refunds.refund_processor import RefundProcessor

# Order status constants (matching OMS constants)
ORDER_STATUS_WMS_SYNCED = 21
ORDER_STATUS_WMS_SYNC_FAILED = 22


@shared_task(bind=True, max_retries=3, default_retry_delay=60)
def sync_order_to_liink_task(self, order_id: str):
    """
    Celery task to sync order to Liink API
    Args:
        order_id (str): The order ID to sync
    Returns:
        dict: Task result with success status and details
    """
    logger.info(f"Starting Liink sync task for order: {order_id}")

    try:
        liink_service = LiinkService()

        # Step 1: Fetch order data from OMS database
        order_data = liink_service.fetch_order_data_from_oms(order_id)
        if not order_data:
            logger.error(f"Failed to fetch order data for {order_id}")
            liink_service.update_oms_order_status(order_id, ORDER_STATUS_WMS_SYNC_FAILED)
            return {
                "success": False,
                "order_id": order_id,
                "error": "Order data not found",
                "message": "Failed to fetch order data from OMS database"
            }

        # Step 2: For POS orders, adjust inventory for any extra quantity
        if order_data.get("order_mode") == "pos":
            try:
                redis_url = getattr(settings, "IMS_REDIS_URL", settings.REDIS_BROKER_URL)
                redis_client = redis.Redis.from_url(redis_url, decode_responses=True)
                facility_code = order_data.get("facility_id")
                warehouse = order_data.get("facility_name")
                wms_inventory_service = WMSInventoryAdjustmentService()
                for item in order_data.get("items", []):
                    extra_qty = item.get("pos_extra_quantity", 0)
                    if extra_qty:
                        sku_code = item.get("wh_sku")
                        stock_key = f"stock:{facility_code}:{sku_code}"
                        stock_qty = int(redis_client.get(stock_key) or 0)
                        required_qty = extra_qty
                        if required_qty > stock_qty:
                            diff_qty = required_qty - stock_qty
                            wms_inventory_service.adjust_inventory(
                                warehouse=warehouse,
                                sku_code=sku_code,
                                quantity=diff_qty,
                                remarks=order_id,
                            )
            except Exception as e:
                logger.error(
                    f"Error adjusting inventory for POS extras in order {order_id}: {e}",
                    exc_info=True,
                )

        # Step 3: Transform order data to Liink format
        liink_payload = liink_service.transform_order_to_liink_format(order_data)
        warehouse = order_data.get("facility_name", "hr009_pla_ls1")

        # Step 4: Create order in Liink (run async function in sync context)
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            liink_result = loop.run_until_complete(
                liink_service.create_order_in_liink(liink_payload, warehouse)
            )
        except Exception as e:
            logger.error(f"Exception while creating order in Liink: {str(e)}")
            liink_service.update_oms_order_status(order_id, ORDER_STATUS_WMS_SYNC_FAILED)
            return {
                "success": False,
                "order_id": order_id,
                "error": str(e),
                "message": "Exception occurred while creating order in Liink"
            }
        finally:
            loop.close()

        # Step 3: Update OMS order status based on result
        if liink_result["success"]:
            liink_service.update_oms_order_status(order_id, ORDER_STATUS_WMS_SYNCED)
            logger.info(f"Order {order_id} synced to Liink successfully")
            publish_event("order_created", "order_created_format", order_data, warehouse)
            return {
                "success": True,
                "order_id": order_id,
                "liink_response": liink_result.get("liink_response"),
                "message": "Order synced to Liink successfully"
            }
        else:
            liink_service.update_oms_order_status(order_id, ORDER_STATUS_WMS_SYNC_FAILED)
            logger.error(f"Order {order_id} failed to sync to Liink: {liink_result.get('message')}")
            return {
                "success": False,
                "order_id": order_id,
                "error": liink_result.get("error"),
                "message": f"Failed to sync order to Liink: {liink_result.get('message')}"
            }

    except Exception as e:
        logger.error(f"Exception in sync_order_to_liink_task for order {order_id}: {str(e)}")

        # Update order status to failed
        try:
            liink_service = LiinkService()
            liink_service.update_oms_order_status(order_id, ORDER_STATUS_WMS_SYNC_FAILED)
        except:
            pass

        # Retry the task if retries are available
        if self.request.retries < self.max_retries:
            logger.info(f"Retrying sync_order_to_liink_task for order {order_id} (attempt {self.request.retries + 1})")
            raise self.retry(exc=e, countdown=60 * (self.request.retries + 1))

        return {
            "success": False,
            "order_id": order_id,
            "error": str(e),
            "message": "Exception occurred while syncing to Liink",
            "retries_exhausted": True
        }

@shared_task(bind=True, max_retries=3, default_retry_delay=60)
def cancel_order_flow(self, order_reference: str, warehouse: str, wms_integration_name: str = 'default'):
    """Cancel an order in WMS using the StockOne API.

    Args:
        order_reference: Reference ID of the order to cancel
        warehouse: Warehouse/hub identifier
        wms_integration_name: Optional WMS integration name

    Returns:
        dict: Result containing success status and details
    """
    logger.info(
        f"Starting WMS cancel task for order_reference: {order_reference}, warehouse: {warehouse}"
    )

    try:
        # Cancel order in WMS
        service = WMSOrderService(wms_integration_name=wms_integration_name)
        response = service.cancel_order(order_reference, warehouse)
        logger.info(f"WMS cancel response: {response}")

        return {
            "success": True,
            "order_reference": order_reference,
            "wms_response": response,
        }

    except Exception as e:
        logger.error(
            f"Exception while cancelling order {order_reference} in WMS: {str(e)}"
        )

        if self.request.retries < self.max_retries:
            raise self.retry(exc=e, countdown=60 * (self.request.retries + 1))

        return {
            "success": False,
            "order_reference": order_reference,
            "error": str(e),
            "retries_exhausted": True,
        }
