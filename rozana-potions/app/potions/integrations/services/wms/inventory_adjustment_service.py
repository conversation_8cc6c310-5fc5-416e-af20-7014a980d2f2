from typing import Dict
from integrations.models import WMSIntegration
from integrations.services.wms.auth_service import (
    WMSAuth,
    WMSAuthenticationError,
    WMSAPIError,
)
from potions.logging.utils import get_app_logger

logger = get_app_logger('wms_inventory_adjustment')


class WMSInventoryAdjustmentService:
    """Service class for WMS inventory adjustment operations."""

    def __init__(self, wms_integration_name: str = 'default'):
        self._init_wms_auth(wms_integration_name)

    def _init_wms_auth(self, integration_name: str) -> None:
        """Initialize WMS authentication using integration settings."""
        try:
            wms_config = WMSIntegration.objects.filter(
                name=integration_name,
                is_active=True,
            ).first()
            if not wms_config:
                wms_config = WMSIntegration.objects.filter(is_active=True).first()
            if not wms_config:
                raise ValueError('No active WMS integration found')
            self.wms_auth = WMSAuth(
                base_url=wms_config.base_url,
                client_id=wms_config.client_id,
                client_secret=wms_config.client_secret,
            )
            logger.info(
                'Initialized WMS inventory adjustment service with integration: %s',
                wms_config.name,
            )
        except Exception as exc:
            logger.error('Failed to initialize WMS authentication: %s', exc)
            raise ValueError(f'WMS authentication initialization failed: {exc}')

    def adjust_inventory(self, warehouse: str, sku_code: str, quantity: float, remarks: str,
                         reason: str = '301', location: str = 'DFLT1') -> Dict:
        """Perform inventory adjustment in WMS for a specific SKU."""
        endpoint = '/api/v1/inventory/adhoc_cycle_count/'
        headers = {'warehouse': warehouse}
        payload = {
            'warehouse': warehouse,
            'skip_callback': True,
            'data_list': [
                {
                    'reason': reason,
                    'remarks': remarks,
                    'location': location,
                    'quantity': quantity,
                    'sku_code': sku_code,
                }
            ],
            'skip_inventory_adjustment': True,
        }

        logger.info('Adjusting inventory for sku=%s warehouse=%s quantity=%s', sku_code, warehouse, quantity)
        try:
            response = self.wms_auth.make_authenticated_request(
                method='POST',
                endpoint=endpoint,
                headers=headers,
                json=payload,
            )
            try:
                data = response.json()
            except ValueError:
                data = {'detail': response.text}
            return {'status_code': response.status_code, 'data': data}
        except (WMSAuthenticationError, WMSAPIError) as exc:
            logger.error('WMS API error adjusting inventory for %s: %s', sku_code, exc)
            raise
