"""
OMS Order Status Update Service

This service handles updating order statuses in the OMS (Order Management System)
database based on TMS webhook events. It provides methods to update order statuses
with proper mapping from TMS event types to OMS status codes.
"""

from django.db import connections
from django.db.utils import DatabaseError
from django.conf import settings
from typing import Dict, Any, Optional, List
from datetime import datetime
from integrations.views.sale_return import process_sales_return_to_wms

# Logger
from potions.logging.utils import get_app_logger
logger = get_app_logger('oms_orders')

# Repositories
from core.repository.orders import order_repository
from core.repository.sales_return import sales_return_repository


class OMSOrderStatusUpdateService:
    """
    Service class to handle OMS order status updates with proper connection management.
    
    This service uses Django's multi-database support with connection pooling
    and automatic connection management for updating order statuses in the OMS orders table.
    """
    
    # TMS event type to OMS status mapping (using actual OMS status codes)
    TMS_TO_OMS_STATUS_MAPPING = {

        # Pickup related statuses
        'accept': 10,                 # OPEN - Order accepted/ready for pickup
        'order_accepted': 10,         # OPEN - Order accepted/ready for pickup
        'added_to_bag': 10,
        'assigned_for_delivery': 10,
        'pickup_awaited': 10,         # OPEN - Order ready for pickup
        'pickup_scheduled': 10,       # OPEN - Pickup scheduled
        'pickup_attempted': 10,       # OPEN - Pickup attempted
        'pickup_started': 10,
        'pickup_failed': 10,         # UNFULFILLED - Pickup failed
        # Delivery related statuses
        'attempted': 13,             # UNFULFILLED - Delivery attempted but failed
        'delivery_attempted': 13,    # UNFULFILLED - Delivery attempted but failed
        'delivered': 35,             # FULFILLED - Successfully delivered
        
        # Return/RTO statuses
        'returned': 15,              # RETURNED - Item returned
        'rto_initiated': 15,         # UNFULFILLED - Return to origin initiated
        'rto_in_transit': 15,        # UNFULFILLED - RTO in transit
        'rto_delivered': 15,         # RETURNED - RTO delivered (item returned)
        
        # Exception statuses
        'lost': 13,                  # UNFULFILLED - Package lost
        'damaged': 13,               # UNFULFILLED - Package damaged
        'cancelled': 14,             # CANCELED - Order cancelled
        
        # Default fallback
        'unknown': 10,               # OPEN - Unknown status
    }
    
    
    def __init__(self, database_alias: str = 'oms'):
        """
        Initialize the OMS database service.
        
        Args:
            database_alias: The database alias configured in Django settings
        """
        self.database_alias = database_alias
        self.connection = None
        
    def get_connection(self):
        """
        Get the database connection using Django's connection management.
        Returns:
            Database connection object
        Raises:
            DatabaseError: If connection cannot be established
        """
        try:
            if not self.connection:
                self.connection = connections[self.database_alias]
            return self.connection
        except Exception as e:
            logger.error(f"Failed to connect to OMS database '{self.database_alias}': {str(e)}")
            raise DatabaseError(f"OMS database connection failed: {str(e)}")

    def get_oms_status_from_tms_event(self, tms_event_type: str) -> int:
        """
        Map TMS event type to OMS status code.
        
        Args:
            tms_event_type: The TMS event type from webhook
            
        Returns:
            int: Corresponding OMS status code
        """
        return self.TMS_TO_OMS_STATUS_MAPPING.get(
            tms_event_type.lower(), 
            self.TMS_TO_OMS_STATUS_MAPPING['unknown']
        )

    def update_order_status_by_reference(self, reference_number: str, tms_event_type: str, 
                                       event_time: Optional[datetime] = None, pieces_detail: List[Dict] = None) -> Dict[str, Any]:
        """
        Update order status using TMS reference number and event type.
        Also updates order_items status based on pieces_detail for delivery events.
        
        Args:
            reference_number (str): The TMS reference number
            tms_event_type (str): The TMS event type
            event_time (datetime, optional): The event timestamp
            pieces_detail (List[Dict], optional): List of pieces with delivery status
            
        Returns:
            dict: Update result with success status and details
        """
        try:
            # Validate inputs
            if not reference_number or not tms_event_type:
                logger.warning(f"Missing reference_number or tms_event_type | reference_number={reference_number} tms_event_type={tms_event_type}")
                return {
                    'success': False,
                    'error': 'Reference number and TMS event type are required',
                    'reference_number': reference_number
                }
            
            # Get OMS status from TMS event type
            oms_status = self.get_oms_status_from_tms_event(tms_event_type)
            
            connection = self.get_connection()
            
            with connection.cursor() as cursor:
                # Find the order by order_id and get the integer ID for order_items
                find_query = """
                    SELECT id, order_id, status 
                    FROM orders 
                    WHERE order_id = %s
                    LIMIT 1
                """
                
                cursor.execute(find_query, [reference_number])
                order_result = cursor.fetchone()
                
                if not order_result:
                    logger.warning(f"No order found with TMS reference number: {reference_number}")
                    return {
                        'success': False,
                        'error': f'Order not found with reference number: {reference_number}',
                        'reference_number': reference_number
                    }
                
                order_pk, order_id, current_status = order_result
                
                # Check for partial delivery if this is a delivered event
                final_order_status = oms_status
                
                if tms_event_type == 'delivered' and pieces_detail:
                    # Check if any items are not delivered (partial delivery)
                    undelivered_items = [p for p in pieces_detail if not p.get('is_delivered', False)]
                    if undelivered_items:
                        # This is a partial delivery
                        final_order_status = 35  # PARTIALLY_FULFILLED
                        logger.info(f"Partial delivery detected for order {order_id}: {len(undelivered_items)} items not delivered")
                
                # Update the orders table
                update_order_query = """
                    UPDATE orders 
                    SET status = %s,
                        updated_at = NOW()
                    WHERE order_id = %s
                """
                
                cursor.execute(update_order_query, [
                    final_order_status,
                    order_id
                ])
                
                order_updated_count = cursor.rowcount
                
                # Update order_items table
                items_updated_count = 0
                item_updates = []
                
                if tms_event_type == 'delivered' and pieces_detail:
                    # For delivery events, update items based on pieces_detail
                    for piece in pieces_detail:
                        piece_sku = piece.get('product_code')
                        is_delivered = piece.get('is_delivered', False)
                        
                        if piece_sku:
                            if is_delivered:
                                item_status = 35  # FULFILLED
                            else:
                                item_status = 13  # UNFULFILLED
                            
                            # Update order item by SKU
                            update_item_query = """
                                UPDATE order_items 
                                SET status = %s,
                                    delivered_quantity = %s,
                                    updated_at = NOW()
                                WHERE order_id = %s AND sku = %s
                            """
                            
                            cursor.execute(update_item_query, [
                                item_status,
                                piece.get('quantity_delivered', 0),
                                order_pk,  # Use integer ID for order_items
                                piece_sku
                            ])
                            
                            item_updated = cursor.rowcount
                            items_updated_count += item_updated
                            item_updates.append({
                                'sku': piece_sku,
                                'status': item_status,
                                'is_delivered': is_delivered,
                                'updated': item_updated > 0
                            })
                else:
                    # For non-delivery events, update all order items with the same status
                    update_all_items_query = """
                        UPDATE order_items 
                        SET status = %s,
                            updated_at = NOW()
                        WHERE order_id = %s
                    """
                    
                    cursor.execute(update_all_items_query, [
                        final_order_status,
                        order_pk
                    ])
                    
                    items_updated_count = cursor.rowcount
                
                if order_updated_count > 0:
                    logger.info(f"Successfully updated order {order_id} status from {current_status} to {final_order_status} "
                              f"and {items_updated_count} order items based on TMS event: {tms_event_type}")
                    
                    result = {
                        'success': True,
                        'order_id': order_id,
                        'reference_number': reference_number,
                        'previous_status': current_status,
                        'new_status': final_order_status,
                        'tms_event_type': tms_event_type,
                        'order_updated_count': order_updated_count,
                        'items_updated_count': items_updated_count
                    }
                    
                    if item_updates:
                        result['item_updates'] = item_updates
                    
                    return result
                else:
                    logger.warning(f"Failed to update order status for reference: {reference_number}")
                    return {
                        'success': False,
                        'error': f'Failed to update order status',
                        'reference_number': reference_number,
                        'order_id': order_id
                    }
                    
        except DatabaseError as e:
            logger.error(f"Database error updating order status for reference {reference_number}: {str(e)}")
            return {
                'success': False,
                'error': f'Database error: {str(e)}',
                'reference_number': reference_number
            }
        except Exception as e:
            logger.error(f"Unexpected error updating order status for reference {reference_number}: {str(e)}")
            return {
                'success': False,
                'error': f'Unexpected error: {str(e)}',
                'reference_number': reference_number
            }

    def update_order_status_by_id(self, order_id: str, tms_event_type: str, 
                                 event_time: Optional[datetime] = None) -> Dict[str, Any]:
        """
        Update order status using order ID and TMS event type.
        
        Args:
            order_id (str): The order ID
            tms_event_type (str): The TMS event type
            event_time (datetime, optional): The event timestamp
            
        Returns:
            dict: Update result with success status and details
        """
        try:
            # Validate inputs
            if not order_id or not tms_event_type:
                logger.warning(f"Missing order_id or tms_event_type | order_id={order_id} tms_event_type={tms_event_type}")
                return {
                    'success': False,
                    'error': 'Order ID and TMS event type are required',
                    'order_id': order_id
                }
            
            # Get OMS status from TMS event type
            oms_status = self.get_oms_status_from_tms_event(tms_event_type)
            
            connection = self.get_connection()
            
            with connection.cursor() as cursor:
                # Get current status first
                cursor.execute("SELECT status FROM orders WHERE order_id = %s", [order_id])
                current_result = cursor.fetchone()
                
                if not current_result:
                    logger.warning(f"No order found with ID: {order_id}")
                    return {
                        'success': False,
                        'error': f'Order not found with ID: {order_id}',
                        'order_id': order_id
                    }
                
                current_status = current_result[0]
                
                # Update the order status
                update_query = """
                    UPDATE orders 
                    SET status = %s,
                        updated_at = NOW()
                    WHERE order_id = %s
                """
                
                cursor.execute(update_query, [
                    oms_status,
                    order_id
                ])
                
                updated_count = cursor.rowcount
                
                if updated_count > 0:
                    logger.info(f"Successfully updated order {order_id} status from {current_status} to {oms_status} "
                              f"based on TMS event: {tms_event_type}")
                    return {
                        'success': True,
                        'order_id': order_id,
                        'previous_status': current_status,
                        'new_status': oms_status,
                        'tms_event_type': tms_event_type,
                        'updated_count': updated_count
                    }
                else:
                    logger.warning(f"Failed to update order status for order ID: {order_id}")
                    return {
                        'success': False,
                        'error': f'Failed to update order status',
                        'order_id': order_id
                    }
                    
        except DatabaseError as e:
            logger.error(f"Database error updating order status for order {order_id}: {str(e)}")
            return {
                'success': False,
                'error': f'Database error: {str(e)}',
                'order_id': order_id
            }
        except Exception as e:
            logger.error(f"Unexpected error updating order status for order {order_id}: {str(e)}")
            return {
                'success': False,
                'error': f'Unexpected error: {str(e)}',
                'order_id': order_id
            }

    def bulk_update_order_statuses(self, updates: list) -> Dict[str, Any]:
        """
        Bulk update multiple order statuses.
        
        Args:
            updates: List of dictionaries with 'reference_number', 'tms_event_type', and optional 'event_time'
            
        Returns:
            dict: Bulk update result with success/failure counts
        """
        results = {
            'success': True,
            'total_processed': len(updates),
            'successful_updates': 0,
            'failed_updates': 0,
            'details': []
        }
        
        for update in updates:
            result = self.update_order_status_by_reference(
                update.get('reference_number'),
                update.get('tms_event_type'),
                update.get('event_time')
            )
            
            if result['success']:
                results['successful_updates'] += 1
            else:
                results['failed_updates'] += 1
                results['success'] = False
            
            results['details'].append(result)
        
        logger.info(f"Bulk update completed: {results['successful_updates']} successful, "
                   f"{results['failed_updates']} failed out of {results['total_processed']} total")
        
        return results

    def process_webhook_payload(self, webhook_payload: Dict[str, Any]) -> Dict[str, Any]:
        
        try:
            reference_number = webhook_payload.get('reference_number')
            consignment_type = webhook_payload.get('consignment_type')
            customer_reference_number = webhook_payload.get('customer_reference_number')
            tms_event_type = webhook_payload.get('type')
            raven_link = webhook_payload.get('raven_link')
            if raven_link:
                separator = '&' if '?' in raven_link else '?'
                raven_link = f"{raven_link}{separator}map_only=true&show_full_map=true"
            
            if not customer_reference_number or not tms_event_type:
                logger.warning(f"Missing customer_reference_number or type in webhook payload | reference_number={reference_number} customer_reference_number={customer_reference_number} type={tms_event_type}")
                return {
                    'success': False,
                    'error': 'Missing customer_reference_number or type in webhook payload',
                    'reference_number': reference_number,
                    'customer_reference_number': customer_reference_number
                }
            
            connection = self.get_connection()
            
            with connection.cursor() as cursor:
                if tms_event_type == 'order_accepted':
                    # Single query to update order and get details atomically
                    update_order_query = """
                        UPDATE orders 
                        SET status = 33, updated_at = NOW() 
                        WHERE order_id = %s
                        RETURNING id, order_id, status
                    """
                    cursor.execute(update_order_query, [customer_reference_number])
                    order_result = cursor.fetchone()
                    
                    if order_result:
                        order_pk, order_id, current_status = order_result
                        logger.info(f"Updated order {order_id} status to 33 (order_accepted)")
                        
                        if raven_link:
                            check_invoice_query = """
                                SELECT id FROM invoice_details WHERE order_id = %s LIMIT 1
                            """
                            cursor.execute(check_invoice_query, [order_pk])
                            invoice_record = cursor.fetchone()
                            
                            if invoice_record:
                                update_invoice_query = """
                                    UPDATE invoice_details 
                                    SET raven_link = %s, updated_at = NOW()
                                    WHERE order_id = %s
                                """
                                cursor.execute(update_invoice_query, [raven_link, order_pk])
                                logger.info(f"Updated raven_link for order {order_id} in invoice_details")
                            else:
                                insert_invoice_query = """
                                    INSERT INTO invoice_details (order_id, invoice_number, raven_link, created_at, updated_at)
                                    VALUES (%s, %s, %s, NOW(), NOW())
                                """
                                cursor.execute(insert_invoice_query, [order_pk, reference_number, raven_link])
                                logger.info(f"Created new invoice_details record for order {order_id} with raven_link")
                        
                        return {
                            'success': True,
                            'message': 'Order accepted event processed successfully',
                            'order_id': order_id,
                            'reference_number': reference_number,
                            'customer_reference_number': customer_reference_number,
                            'raven_link': raven_link,
                            'order_updated': True,
                            'invoice_updated': bool(raven_link)
                        }
                    else:
                        # Order not found - only create invoice if raven_link provided
                        if raven_link:
                            insert_invoice_query = """
                                INSERT INTO invoice_details (invoice_number, raven_link, order_id, created_at, updated_at)
                                VALUES (%s, %s, %s, NOW(), NOW())
                            """
                            cursor.execute(insert_invoice_query, [reference_number, raven_link, customer_reference_number])
                            logger.info(f"Created invoice_details for customer reference {customer_reference_number} (order not found)")
                            
                            return {
                                'success': True,
                                'message': 'Invoice details created for order_accepted event (order not found)',
                                'reference_number': reference_number,
                                'customer_reference_number': customer_reference_number,
                                'raven_link': raven_link,
                                'invoice_created': True
                            }
                        else:
                            logger.warning(f"Order {customer_reference_number} not found and no raven_link provided")
                            return {
                                'success': False,
                                'error': 'Order not found and no raven_link provided',
                                'reference_number': reference_number,
                                'customer_reference_number': customer_reference_number
                            }
                
                elif tms_event_type.lower() == 'delivered' and consignment_type == "forward":
                    # Use a single CTE to update orders and order_items atomically and get counts
                    cte_query = """
                    WITH updated_order AS (
                      UPDATE orders
                      SET status = 35, updated_at = NOW()
                      WHERE order_id = %s
                      RETURNING id
                    ),
                    updated_items AS (
                      UPDATE order_items
                      SET status = 35, updated_at = NOW()
                      WHERE order_id IN (SELECT id FROM updated_order)
                      RETURNING 1
                    )
                    SELECT (SELECT COUNT(*) FROM updated_order) AS orders_updated,
                           (SELECT COUNT(*) FROM updated_items) AS items_updated;
                    """
                    cursor.execute(cte_query, [customer_reference_number])
                    result = cursor.fetchone()
                    orders_updated = (result[0] if result else 0) or 0
                    items_updated = (result[1] if result else 0) or 0

                    if orders_updated > 0:
                        logger.info(
                            f"Updated order {customer_reference_number} status to FULFILLED (35) for delivered event; "
                            f"orders_updated={orders_updated}, items_updated={items_updated}"
                        )
                        return {
                            'success': True,
                            'message': 'Order status updated to FULFILLED',
                            'customer_reference_number': customer_reference_number,
                            'new_status': 35,
                            'tms_event_type': tms_event_type,
                            'updated_count': orders_updated,
                            'items_updated': items_updated,
                        }
                    else:
                        logger.warning(f"Delivered event but no orders updated | customer_reference_number={customer_reference_number}")
                        return {
                            'success': False,
                            'error': f'Order not found with customer reference number: {customer_reference_number}',
                            'customer_reference_number': customer_reference_number
                        }
                        
                elif tms_event_type.lower() == 'attempted':
                    update_order_query = """
                        UPDATE orders 
                        SET status = 13, updated_at = NOW() 
                        WHERE order_id = %s
                    """
                    cursor.execute(update_order_query, [customer_reference_number])
                    updated_count = cursor.rowcount
                    
                    if updated_count > 0:
                        logger.info(f"Updated order {customer_reference_number} status to UNFULFILLED (13) for attempted event")
                        return {
                            'success': True,
                            'message': 'Order status updated to UNFULFILLED',
                            'customer_reference_number': customer_reference_number,
                            'new_status': 13,
                            'tms_event_type': tms_event_type,
                            'updated_count': updated_count
                        }
                    else:
                        logger.warning(f"Attempted event but no orders updated | customer_reference_number={customer_reference_number}")
                        return {
                            'success': False,
                            'error': f'Order not found with customer reference number: {customer_reference_number}',
                            'customer_reference_number': customer_reference_number
                        }
                
                elif tms_event_type.lower() == 'pickup_completed':
                    update_order_query = """
                        UPDATE orders 
                        SET status = 34, updated_at = NOW() 
                        WHERE order_id = %s
                    """
                    cursor.execute(update_order_query, [customer_reference_number])
                    updated_count = cursor.rowcount
                    
                    if updated_count > 0:
                        logger.info(f"Updated order {customer_reference_number} status to Out for Delivery (34) for delivered event")
                        return {
                            'success': True,
                            'message': 'Order status updated to OutForDelivery',
                            'customer_reference_number': customer_reference_number,
                            'new_status': 34,
                            'tms_event_type': tms_event_type,
                            'updated_count': updated_count
                        }
                    else:
                        logger.warning(f"Pickup completed event but no orders updated | customer_reference_number={customer_reference_number}")
                        return {
                            'success': False,
                            'error': f'Order not found with customer reference number: {customer_reference_number}',
                            'customer_reference_number': customer_reference_number
                        }
                
                elif tms_event_type.lower() == 'rto_initiated':
                    # Update order status to RETURNED/Initiated (15 as per mapping)
                    updated = order_repository.update_order_status(customer_reference_number, 15)
                    updated_count = 1 if updated else 0
                    
                    if updated:
                        logger.info(f"Updated order {customer_reference_number} status to RETURNED (15) for RTO initiated event")
                        # Also update all order_items to status 15 via repository to keep item statuses in sync
                        try:
                            rto_items_updated = order_repository.update_all_order_items_status(customer_reference_number, 15)
                            logger.info(f"Updated {rto_items_updated} order_items to 15 for RTO initiated | order_id={customer_reference_number}")
                        except Exception as e:
                            rto_items_updated = 0
                            logger.error(f"Failed to update order_items for RTO initiated {customer_reference_number}: {e}", exc_info=True)
                        
                        # Create RTO return record (RTO scenarios don't have existing return records)
                        logger.info(f"Creating RTO return record for order {customer_reference_number}")
                        return_error = None
                        try:
                            created_return_ref = sales_return_repository.create_rto_return_for_order(customer_reference_number)
                            logger.info(f"DEBUG: Return creation result: {created_return_ref}")
                            if created_return_ref:
                                logger.info(f"Created RTO return {created_return_ref} for order {customer_reference_number}")
                            else:
                                logger.warning(f"Failed to create RTO return for order {customer_reference_number}", exc_info=True)
                        except Exception as e:
                            return_error = str(e)
                            logger.error(f"Exception during return creation for order {customer_reference_number}: {return_error}", exc_info=True)
                            created_return_ref = None
                        
                        return {
                            'success': True,
                            'message': 'Order status updated for RTO initiated',
                            'customer_reference_number': customer_reference_number,
                            'new_status': 15,
                            'tms_event_type': tms_event_type,
                            'updated_count': updated_count,
                            'items_updated': rto_items_updated,
                            'return_created': bool(created_return_ref),
                            'return_reference': created_return_ref,
                            'return_error': return_error
                        }
                    else:
                        return {
                            'success': False,
                            'error': f'Order not found with customer reference number: {customer_reference_number}',
                            'customer_reference_number': customer_reference_number
                        }

                elif (tms_event_type.lower() == 'rto_delivered') or (tms_event_type.lower() == 'delivered' and consignment_type=="reverse" ):
                    # Update order status to RETURNED (15)
                    updated = order_repository.update_order_status(customer_reference_number, 15)
                    updated_count = 1 if updated else 0
                    
                    if updated:
                        # Determine return type for logging
                        return_type = "CIR" if (tms_event_type.lower() == 'delivered' and consignment_type == "reverse") else "RTO"
                        logger.info(f"Updated order {customer_reference_number} status to RETURNED (15) for {return_type} delivered event")
                        # Also update all order_items to status 15 via repository to keep item statuses in sync
                        try:
                            reverse_items_updated = order_repository.update_all_order_items_status(customer_reference_number, 15)
                            logger.info(f"Updated {reverse_items_updated} order_items to 15 for {return_type} delivered | order_id={customer_reference_number}")
                        except Exception as e:
                            reverse_items_updated = 0
                            logger.error(f"Failed to update order_items for {return_type} delivered {customer_reference_number}: {e}", exc_info=True)
                        
                        # Process sales return to WMS
                        try:
                            return_result = sales_return_repository.get_latest_return_reference_and_facility_by_order_id(
                                customer_reference_number
                            )
                            
                            if return_result:
                                return_reference = return_result.get('return_reference')
                                facility_name = return_result.get('facility_name')
                                
                                sales_return_result = process_sales_return_to_wms(
                                    return_reference=return_reference,
                                    warehouse=facility_name
                                )
                                
                                logger.info(f"Sales return processed for {return_type} order {customer_reference_number}: {sales_return_result}")
                                
                                return {
                                    'success': True,
                                    'message': f'Order status updated to RETURNED and sales return processed ({return_type})',
                                    'customer_reference_number': customer_reference_number,
                                    'new_status': 15,
                                    'tms_event_type': tms_event_type,
                                    'updated_count': updated_count,
                                    'items_updated': reverse_items_updated,
                                    'sales_return_result': sales_return_result
                                }
                            else:
                                return {
                                    'success': True,
                                    'message': f'Order status updated to RETURNED but no return record found for {return_type} processing',
                                    'customer_reference_number': customer_reference_number,
                                    'new_status': 15,
                                    'tms_event_type': tms_event_type,
                                    'updated_count': updated_count,
                                    'items_updated': reverse_items_updated,
                                    'sales_return_error': 'No return record found'
                                }
                            
                        except Exception as e:
                            logger.error(f"Failed to process sales return for {return_type} order {customer_reference_number}: {str(e)}", exc_info=True)
                            return {
                                'success': True,  # Order status was updated successfully
                                'message': f'Order status updated to RETURNED but sales return processing failed ({return_type})',
                                'customer_reference_number': customer_reference_number,
                                'new_status': 15,
                                'tms_event_type': tms_event_type,
                                'updated_count': updated_count,
                                'sales_return_error': str(e)
                            }
                    else:
                        return {
                            'success': False,
                            'error': f'Order not found with customer reference number: {customer_reference_number}',
                            'customer_reference_number': customer_reference_number
                        }
                
                else:
                    return {
                        'success': True,
                        'message': f'Event type {tms_event_type} received but no specific handling implemented',
                        'customer_reference_number': customer_reference_number,
                        'tms_event_type': tms_event_type
                    }
                    
        except DatabaseError as e:
            logger.error(f"Database error processing webhook for customer reference {customer_reference_number}: {str(e)}")
            return {
                'success': False,
                'error': f'Database error: {str(e)}',
                'reference_number': reference_number,
                'customer_reference_number': customer_reference_number
            }
        except Exception as e:
            logger.error(f"Unexpected error processing webhook for customer reference {customer_reference_number}: {str(e)}")
            return {
                'success': False,
                'error': f'Unexpected error: {str(e)}',
                'reference_number': reference_number,
                'customer_reference_number': customer_reference_number
            }

    # Removed _create_return_for_undelivered_items method as return creation is no longer required


# Convenience functions for quick access
# def update_order_status_by_reference(reference_number: str, tms_event_type: str, 
#                                    event_time: Optional[datetime] = None) -> Dict[str, Any]:
#     """
#     Convenience function to update order status by TMS reference number.
    
#     Args:
#         reference_number: The TMS reference number
#         tms_event_type: The TMS event type
#         event_time: Optional event timestamp
        
#     Returns:
#         dict: Result dictionary with success status and details
#     """
#     service = OMSOrderStatusUpdateService()
#     return service.update_order_status_by_reference(reference_number, tms_event_type, event_time)


# def update_order_status_by_id(order_id: str, tms_event_type: str, 
#                             event_time: Optional[datetime] = None) -> Dict[str, Any]:
#     """
#     Convenience function to update order status by order ID.
    
#     Args:
#         order_id: The order ID
#         tms_event_type: The TMS event type
#         event_time: Optional event timestamp
        
#     Returns:
#         dict: Result dictionary with success status and details
#     """
#     service = OMSOrderStatusUpdateService()
#     return service.update_order_status_by_id(order_id, tms_event_type, event_time)


def process_webhook_payload(webhook_payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    Convenience function to process complete TMS webhook payload.
    
    Args:
        webhook_payload: Complete webhook payload from TMS
        
    Returns:
        dict: Result dictionary with order and item update details
    """
    service = OMSOrderStatusUpdateService()
    return service.process_webhook_payload(webhook_payload)
