from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.conf import settings
from core.repository.orders import order_repository
from core.repository.invoice_details import invoice_details_repository
from integrations.tasks.forward_consignment import create_forward_consignment_task
from integrations.tasks.order_refunds import process_cancelled_and_unfulfilled_quantity_refunds_task
from payments.constants import OrderStatus

# Logger
from potions.logging.utils import get_app_logger
logger = get_app_logger('order_update_api')

class OrderUpdateAPIView(APIView):

    def map_wms_status(self, wms_status: str) -> int:
        """
        Map WMS status to OMS status code
        Args:
            wms_status: Status from WMS
        Returns:
            int: Corresponding OMS status code
        """
        if not wms_status:
            return None

        mapped_status = OrderStatus.WMS_STATUS_MAP.get(wms_status.lower())
        if mapped_status is None:
            logger.error(f"Unknown WMS status: {wms_status}")
            return None

        logger.info(f"Mapped WMS status '{wms_status}' to OMS status {mapped_status}")
        return mapped_status

    def extract_invoice_number(self, items: list) -> str:
        """
        Extract invoice number from items array, ensuring consistency across all items
        Args:
            items: List of items from the payload
        Returns:
            str: The first invoice number found, or None if no invoice details exist
        """
        if not items:
            logger.warning("No items found in payload for invoice extraction")
            return None

        first_invoice_number = None
        seen_invoice_numbers = set()

        for item in items:
            invoice_details = item.get('invoice_details', [])
            if not invoice_details:
                continue

            for invoice_detail in invoice_details:
                invoice_number = invoice_detail.get('invoice_number')
                if invoice_number:
                    seen_invoice_numbers.add(invoice_number)
                    if first_invoice_number is None:
                        first_invoice_number = invoice_number

        # Check for conflicts and log error if multiple different invoice numbers found
        if len(seen_invoice_numbers) > 1:
            logger.error(f"Multiple different invoice numbers found in payload: {list(seen_invoice_numbers)}. Using first seen invoice number: {first_invoice_number}")
        elif len(seen_invoice_numbers) == 1:
            logger.info(f"Found consistent invoice number across all items: {first_invoice_number}")
        else:
            logger.warning("No invoice numbers found in any item's invoice_details")

        return first_invoice_number

    def post(self, request, wms_integration_name: str):
        try:
            facility_name = request.data.get('warehouse')
            data = request.data.get('data', {})
            order_id = data.get('order_reference')
            items = data.get('items', [])
            order_status = data.get('order_status')
            logger.info(f"order_update_received | wms_integration={wms_integration_name} | order_id={order_id} | items_count={len(items) if isinstance(items, list) else 0} | order_status={order_status}")

            if not order_id or not items:
                logger.warning("order_update_validation_failed | reason=missing_order_id_or_items")
                return Response({'error': 'Missing order_id or items'}, status=status.HTTP_400_BAD_REQUEST)

            # Extract invoice number from items
            invoice_number = self.extract_invoice_number(items)

            # Check Ordr exists
            order_record = order_repository.get_order_by_id(order_id)
            if not order_record:
                logger.error(f"order_update_order_not_found | order_id={order_id}")
                return Response({'error': 'Order not found in OMS'}, status=status.HTTP_404_NOT_FOUND)


            order_mode = order_record.get('order_mode')

            for item in items:
                sku_code = item.get('sku_code')
                unfulfilled_quantity = item.get('unfulfilled_quantity')
                cancelled_quantity = item.get('cancelled_quantity')
                order_quantity = item.get('order_quantity')
                fulfilled_quantity = item.get('picked_quantity') # we are using picked quantity as fulfilled quantity
                pack_uom_quantity = int(item.get('pack_uom_quantity', 1))
                full_cancel_flag = False
                if order_quantity == cancelled_quantity:
                    full_cancel_flag = True

                # Skip if no SKU code
                if not sku_code:
                    logger.warning(f"order_update_item_skipped | order_id={order_id} | reason=missing_sku_code")
                    continue

                if unfulfilled_quantity is None or cancelled_quantity is None or fulfilled_quantity is None:
                    logger.warning(f"order_update_item_skipped | order_id={order_id} | reason=missing_quantities")
                    continue

                logger.info(f"order_update_item_quantities | order_id={order_id} | sku_code={sku_code} | unfulfilled_quantity={unfulfilled_quantity} | cancelled_quantity={cancelled_quantity} | fulfilled_quantity={fulfilled_quantity}")

                # Map WMS item status if provided
                item_status = item.get('status')
                mapped_item_status = self.map_wms_status(item_status)
                if order_mode == "pos" and item_status in ["fulfilled", "partial_fulfilled", "invoiced"]:
                    mapped_item_status = OrderStatus.TMS_DELIVERED

                # Determine final status for item
                final_item_status = None
                if full_cancel_flag:
                    final_item_status = OrderStatus.CANCELLED
                elif mapped_item_status:
                    final_item_status = mapped_item_status

                # Single database call to update all quantities
                updated = order_repository.update_order_item_quantities(
                    order_id=order_id,
                    sku_code=sku_code,
                    fulfilled_quantity=float(fulfilled_quantity),
                    cancelled_quantity=float(cancelled_quantity),
                    unfulfilled_quantity=float(unfulfilled_quantity),
                    new_status=final_item_status,
                    pack_uom_quantity=pack_uom_quantity
                )

                if not updated:
                    logger.error(f"order_update_no_rows_affected | order_id={order_id} | sku={sku_code}")

            # Update order status based on WMS order status
            if order_status:
                mapped_order_status = self.map_wms_status(order_status)
                if order_mode == "pos" and order_status.lower() == 'invoiced':
                    mapped_order_status = OrderStatus.TMS_DELIVERED
                if mapped_order_status:
                    order_updated = order_repository.update_order_status(order_id, mapped_order_status)
                    logger.info(f"order_updated | order_id={order_id} | wms_status={order_status} | mapped_status={mapped_order_status}, updated={order_updated}")
                else:
                    logger.error(f"order_status_mapping_failed | order_id={order_id} | wms_status={order_status}")

            # Update invoice details if invoice number exists
            if invoice_number:
                internal_order_id = order_repository.get_internal_order_id(order_id)
                if internal_order_id and not invoice_details_repository.get_by_order_id(internal_order_id):
                    invoice_details_repository.create_invoice_details(invoice_number, internal_order_id)
                    logger.info(f"invoice_details_created | order_id={order_id} | invoice_number={invoice_number}")

            # Initiate the refund for cancelled and unfulfilled items
            process_cancelled_and_unfulfilled_quantity_refunds_task.apply_async(args=[order_id, facility_name])

            order_status = data.get('order_status')
            warehouse = data.get('warehouse')
            order_data = order_repository.get_order_by_id(order_id)
            order_mode = order_data.get('order_mode') if order_data else None
            task_id = None
            if order_status and order_status.lower() == 'invoiced' and order_mode and order_mode.lower() != 'pos':
                task = create_forward_consignment_task.apply_async(args=[data.get('order_reference') or order_id, warehouse, None, wms_integration_name, request.data])
                task_id = task.id
                logger.info(f"order_update_forward_cn_created | order_id={order_id} | order_mode={order_mode} | task_id={task_id}")

            return Response({'success': True, 'order_id': order_id, 'forward_cn_task_id': task_id}, status=status.HTTP_200_OK)

        except Exception as e:
            logger.exception(f"order_update_unexpected_error | order_id={locals().get('order_id', None)} | error={str(e)}")
            return Response({'error': 'Internal Server Error'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
