from typing import List, Optional
from decimal import Decimal

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status

from potions.logging.utils import get_app_logger
from core.repository.sales_return import sales_return_repository
from core.repository.orders import order_repository
from payments.refunds.refund_processor import RefundProcessor

from integrations.tasks.order_refunds import process_returned_quantity_refunds_task

logger = get_app_logger('grn_callback')


class GRNCallbackAPIView(APIView):
    """
    API endpoint to receive GRN (Goods Receipt Note) callbacks from WMS after a sales return.
    """ 

    def post(self, request, wms_integration_name: Optional[str] = None):
        try:
            payload = request.data or {}

            # GRN payload is at root level (no 'data' wrapper)
            # Prefer 'sales_return_reference' (e.g., RTN-...), fallback to 'sales_return_id' (e.g., SR-...)
            return_reference = payload.get('sales_return_reference')
            order_id = payload.get('reference_number')
            facility_name = payload.get('warehouse')
            if not return_reference:
                return Response({
                    'error': 'Missing sales_return_reference or sales_return_id in payload'
                }, status=status.HTTP_400_BAD_REQUEST)

            items = payload.get('items') or []
            return_skus = []
            if isinstance(items, list) and items:
                for item in items:
                    return_item_data = {}
                    sku_code = str(item.get('sku_code')).strip()
                    accepted_quantity = item.get('accepted_quantity', 0)
                    pack_uom_quantity = item.get('pack_uom_quantity', 0)

                    return_item_data = {
                        'sku_code': sku_code,
                        'accepted_quantity': accepted_quantity,
                        'pack_uom_quantity': pack_uom_quantity
                    }   

                    return_skus.append(return_item_data)

            updated_count = 0
            # Get return items by reference
            for return_sku in return_skus:
                sku_code = return_sku['sku_code']
                pack_uom_quantity = return_sku['pack_uom_quantity']
                return_items = sales_return_repository.get_return_items_by_wh_sku_and_pack_uom(return_reference, sku_code, pack_uom_quantity)

                if return_items:
                    # update the return item status to completed and accepted_quantity to accepted_quantity
                    return_item = return_items[0]
                    accepted_quantity = return_sku['accepted_quantity']
                    return_item_id = return_item['return_item_id']
                    updated_count = sales_return_repository.update_return_item_by_id(
                        return_item_id=return_item_id,
                        accepted_quantity=accepted_quantity,
                        status='completed'
                    )

                    if updated_count and updated_count > 0:
                        logger.info(f"Updated return item {return_item_id} to 'completed' | accepted_quantity={accepted_quantity}")
                        updated_count += 1
                    else:
                        logger.error(f"Failed to update return item {return_item_id} to 'completed' | accepted_quantity={accepted_quantity}")


            # If any items were updated to completed, also mark the return header as completed
            header_updated = 0
            if updated_count and updated_count > 0:
                try:
                    header_updated = sales_return_repository.update_return_status(return_reference=return_reference, status='completed')
                    logger.info(f"Updated return header to 'completed' | reference={return_reference}, header_updated={header_updated}")
                except Exception as e:
                    logger.error(f"Failed to update return header to 'completed' for {return_reference}: {e}", exc_info=True)


            #process return refunds
            process_returned_quantity_refunds_task.apply_async(args=[return_reference, order_id, facility_name])

            logger.info(f"GRN callback processed for integration={wms_integration_name}, return_reference={return_reference}, items_filtered={bool(return_skus)}, updated_rows={updated_count}")
            response_data = {
                'message': 'GRN processed successfully',
                'wms_integration_name': wms_integration_name,
                'return_reference': return_reference,
                'updated_items': updated_count,
                'updated_header': bool(header_updated),
                'filtered_by_skus': bool(return_skus)
            }

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error("Error processing GRN callback: %s", e, exc_info=True)
            return Response({
                'error': 'Internal server error',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
