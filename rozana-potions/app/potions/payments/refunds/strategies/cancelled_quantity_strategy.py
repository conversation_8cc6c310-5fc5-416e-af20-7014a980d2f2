from decimal import Decimal
from typing import Dict, List, Any
from payments.refunds.strategies.base_strategy import RefundStrategy, RefundCalculationResult
from core.repository.sales_return import sales_return_repository

# Logger
from potions.logging.utils import get_app_logger
logger = get_app_logger('refund_processor_cancelled_quantity_strategy')


class CancelledQuantityRefundStrategy(RefundStrategy):
    """Strategy for processing refunds based on cancelled quantities"""
    
    def __init__(self):
        super().__init__()
        self.sales_return_repository = sales_return_repository

    def get_refund_calculation(self, order_id: str, **kwargs) -> RefundCalculationResult:
        """Calculate refund amount based on cancelled quantity"""
        log_prefix = self.get_log_prefix()

        # Get order items with cancelled quantity > refunded quantity
        items_to_refund = self.order_repository.get_order_items_with_cancelled_quantity(order_id)

        if not items_to_refund:
            logger.info(f"{log_prefix} No items with pending cancelled quantity refunds for order {order_id}")
            return RefundCalculationResult(
                total_amount=Decimal('0'),
                item_details=[]
            )

        logger.info(f"{log_prefix} Found {len(items_to_refund)} items requiring refunds")

        # Calculate total refund amount needed
        total_refund_needed = Decimal('0')
        item_refunds = []

        for item in items_to_refund:
            cancelled_qty = Decimal(str(item.get('cancelled_quantity', 0)))
            refunded_qty = Decimal(str(item.get('refunded_quantity', 0)))
            total_qty = Decimal(str(item.get('quantity', 0)))
            sale_price = Decimal(str(item.get('sale_price', 0)))

            # Calculate pending refund quantity and amount
            pending_refund_qty = cancelled_qty - refunded_qty
            if pending_refund_qty > 0:
                refund_amount = pending_refund_qty * sale_price
                total_refund_needed += refund_amount

                item_refunds.append({
                    'item_id': item['id'],
                    'sku': item.get('sku', ''),
                    'pending_refund_qty': float(pending_refund_qty),
                    'refund_amount': float(refund_amount),
                    'new_refunded_qty': float(cancelled_qty)
                })

        return RefundCalculationResult(
            total_amount=total_refund_needed,
            item_details=item_refunds
        )

    def update_post_refund_status(self, order_id: str, success: bool, item_details: List[Dict[str, Any]], **kwargs) -> None:
        """Update order item refunded quantities after successful cancelled quantity refund"""
        log_prefix = self.get_log_prefix()

        if not success:
            logger.info(f"{log_prefix} Refunds failed for order {order_id}, skipping quantity updates")
            return

        logger.info(f"{log_prefix} Updating refunded quantities for order {order_id}")

        # Update refunded_quantity for each item that was successfully refunded
        for item_detail in item_details:
            try:
                item_id = item_detail.get('item_id')
                refunded_qty = item_detail.get('pending_refund_qty', 0)

                if not item_id or refunded_qty <= 0:
                    continue

                # Get current refunded quantity and add the newly refunded amount
                current_refunded_qty = self.order_repository.get_order_item_refunded_quantity(order_id, item_id)
                new_refunded_qty = current_refunded_qty + refunded_qty

                # Update database
                if self.order_repository.update_order_item_refunded_quantity(item_id, new_refunded_qty):
                    logger.info(f"{log_prefix} Updated item {item_id}: {current_refunded_qty} -> {new_refunded_qty} (+{refunded_qty})")
                else:
                    logger.error(f"{log_prefix} Failed to update refunded_quantity for item {item_id}")

            except Exception as e:
                logger.error(f"{log_prefix} Error updating refunded quantity for item {item_detail}: {e}")
