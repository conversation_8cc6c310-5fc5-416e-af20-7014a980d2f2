from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from decimal import Decimal
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timezone
from pydantic import BaseModel

# Repository and service imports
from core.repository.payments import PaymentRepository
from core.repository.refunds_details import RefundRepository
from core.repository.orders import OrderRepository
from payments.services.razorpay_service import RazorpayService
from payments.services.wallet_service import WalletService
from payments.constants import PaymentStatus, RefundStatus


# Logger
from potions.logging.utils import get_app_logger
logger = get_app_logger('refund_processor_base_strategy')


class RefundProcessorReturnMessage(BaseModel):
    success: bool
    message: str
    data: Optional[Dict] = None


@dataclass
class RefundCalculationResult:
    """Result of refund calculation containing amount and item details"""
    total_amount: Decimal
    item_details: List[Dict[str, Any]]
    additional_data: Dict[str, Any] = field(default_factory=dict)


class RefundStrategy(ABC):
    """Abstract base class for different refund strategies with common payment processing"""

    def __init__(self):
        # Initialize dependencies directly
        self.payment_repository = PaymentRepository()
        self.refund_repository = RefundRepository()
        self.order_repository = OrderRepository()
        self.razorpay_service = RazorpayService()
        self.wallet_service = WalletService()

    @abstractmethod
    def get_refund_calculation(self, order_id: str, **kwargs) -> RefundCalculationResult:
        """
        Calculate refund amount and get items to refund for this strategy

        Args:
            order_id: The order ID to process
            **kwargs: Additional parameters specific to the strategy

        Returns:
            RefundCalculationResult with total amount and item details
        """
        pass

    @abstractmethod
    def update_post_refund_status(self, order_id: str, success: bool, item_details: List[Dict[str, Any]], **kwargs) -> None:
        """
        Update statuses after refund processing

        Args:
            order_id: The order ID that was processed
            success: Whether all refunds were successful
            item_details: List of items that were processed
            **kwargs: Additional parameters specific to the strategy
        """
        pass

    def process_refund(self, order_id: str, **kwargs):
        """Main processing method - handles entire refund flow"""
        try:
            log_prefix = self.get_log_prefix()

            # Step 1: Get refund calculation
            calculation = self.get_refund_calculation(order_id, **kwargs)

            if calculation.total_amount <= 0:
                return RefundProcessorReturnMessage(
                    success=True,
                    message="No refund needed",
                    data={
                        "order_id": order_id,
                        "total_refund_needed": 0,
                        "total_refund_processed": 0,
                        "remaining_refund": 0,
                        "item_details": calculation.item_details,
                        "refund_results": [],
                        "processed_at": datetime.now(timezone.utc).isoformat(),
                        **calculation.additional_data
                    }
                )

            # Step 2: Get customer ID
            customer_id = self.order_repository.get_customer_id_by_order_id(order_id)
            if not customer_id:
                return RefundProcessorReturnMessage(success=False, message=f"No customer found for order {order_id}")

            # Step 3: Process payments
            success, refund_results, remaining_refund = self.process_payments(
                order_id, customer_id, calculation.total_amount, **kwargs
            )

            # Step 4: Update statuses
            try:
                self.update_post_refund_status(
                    order_id=order_id,
                    success=success,
                    item_details=calculation.item_details,
                    total_refund_processed=float(calculation.total_amount - remaining_refund),
                    refund_results=refund_results,
                    **kwargs
                )
            except Exception as e:
                logger.error(f"{log_prefix} Error updating post-refund status: {e}")

            # Step 5: Return result
            result_data = {
                "success": success,
                "order_id": order_id,
                "total_refund_needed": float(calculation.total_amount),
                "total_refund_processed": float(calculation.total_amount - remaining_refund),
                "remaining_refund": float(remaining_refund),
                "item_details": calculation.item_details,
                "refund_results": refund_results,
                "message": self.get_success_message() if success else self.get_partial_message(),
                "processed_at": datetime.now(timezone.utc).isoformat(),
                **calculation.additional_data
            }
            return RefundProcessorReturnMessage(success=success, message=result_data["message"], data=result_data)

        except Exception as e:
            logger.error(f"{log_prefix} Error processing refund: {str(e)}")
            return RefundProcessorReturnMessage(success=False, message=f"Error processing refund: {str(e)}")

    def process_payments(self, order_id: str, customer_id: str, total_amount: Decimal, **kwargs) -> Tuple[bool, List[Dict], Decimal]:
        """Process refunds across multiple payments"""
        # Get completed payments
        all_payments = self.payment_repository.get_payment_details_by_order_id(order_id)
        completed_payments = [p for p in all_payments if p.get('payment_status') == PaymentStatus.COMPLETED]

        if not completed_payments:
            return False, [], total_amount

        # Calculate remaining amounts per payment
        refunded_amounts = self.refund_repository.get_refunded_amounts_by_order_id(order_id)
        available_payments = []

        for payment in completed_payments:
            payment_id = payment.get('id')
            payment_amount = Decimal(str(payment.get('payment_amount', 0)))
            already_refunded = refunded_amounts.get(payment_id, Decimal('0'))
            remaining_amount = payment_amount - already_refunded

            if remaining_amount > 0:
                payment['remaining_refundable'] = remaining_amount
                available_payments.append(payment)

        if not available_payments:
            return False, [], total_amount

        # Sort by priority (Razorpay first, then others)
        is_pos_order = kwargs.get('is_pos_order', False)
        if is_pos_order:
            available_payments.sort(key=lambda p: p.get('payment_date', ''))
        else:
            available_payments.sort(key=lambda p: (0 if p.get('payment_mode', '').lower() == 'razorpay' else 1))

        # Process refunds
        remaining_refund = total_amount
        refund_results = []

        for payment in available_payments:
            if remaining_refund <= 0:
                break

            refund_amount = min(remaining_refund, payment['remaining_refundable'])
            if refund_amount > 0:
                if self.process_single_payment_refund(payment, order_id, customer_id, float(refund_amount), is_pos_order):
                    remaining_refund -= refund_amount
                    refund_results.append({"payment_id": payment.get('id'), "amount": float(refund_amount)})

        success = remaining_refund <= 0
        return success, refund_results, remaining_refund

    def process_single_payment_refund(self, payment: Dict, order_id: str, customer_id: str, amount: float, is_pos_order: bool) -> bool:
        """Process refund for a single payment"""
        try:
            payment_mode = payment.get('payment_mode', '').lower()

            # POS orders always use wallet, others use payment mode
            if is_pos_order or payment_mode in ['cash', 'wallet', 'cod']:
                return self.process_wallet_refund(payment, order_id, customer_id, amount)
            elif payment_mode == 'razorpay':
                return self.process_razorpay_refund(payment, order_id, amount)
            return False
        except Exception as e:
            logger.error(f"Error processing single payment refund: {e}")
            return False

    def process_razorpay_refund(self, payment: Dict, order_id: str, amount: float) -> bool:
        """Process Razorpay refund"""
        try:
            razorpay_result = self.razorpay_service.create_refund(
                payment.get('payment_id'),
                {"notes": f"Refund for order {order_id}"},
                amount
            )

            if razorpay_result.get('success'):
                refund_data = razorpay_result.get('refund')
                self.refund_repository.create_refund_record(
                    payment_id=payment.get('id'),
                    refund_id=refund_data.get('id'),
                    refund_amount=amount,
                    refund_status=RefundStatus.CREATED
                )
                self.refund_repository.update_refund_status(refund_data.get('id'), RefundStatus.CREATED)
                return True

            return False
        except Exception as e:
            logger.error(f"Error processing Razorpay refund: {e}")
            return False

    def process_wallet_refund(self, payment: Dict, order_id: str, customer_id: str, amount: float) -> bool:
        """Process wallet refund"""
        try:
            if not self.wallet_service:
                return False

            wallet_result = self.wallet_service.add_wallet_entry(
                customer_id=str(customer_id),
                amount=amount,
                order_id=order_id,
                payment_id=payment.get('payment_id'),
                entry_type="credit",
                reference_type="order_refund",
                description=f"Refund for {payment.get('payment_mode')} payment - Order {order_id}"
            )

            if wallet_result.success:
                transaction_id = wallet_result.data.get('transaction_id')
                wallet_refund_id = f"wallet_refund_{transaction_id if transaction_id else payment.get('payment_id')}"
                self.refund_repository.create_refund_record(payment.get('id'), wallet_refund_id, amount)
                self.refund_repository.update_refund_status(wallet_refund_id, RefundStatus.PROCESSED)
                return True

            return False
        except Exception as e:
            logger.error(f"Error processing wallet refund: {e}")
            return False

    def get_log_prefix(self) -> str:
        """Get logging prefix for this strategy"""
        return f"[{self.__class__.__name__.upper().replace('REFUNDSTRATEGY', '_REFUND')}]"

    def get_success_message(self) -> str:
        """Get success message for this strategy"""
        strategy_name = self.__class__.__name__.replace('RefundStrategy', '').lower()
        return f"{strategy_name.replace('_', ' ').title()} refunds processed successfully"

    def get_partial_message(self) -> str:
        """Get partial success message for this strategy"""
        strategy_name = self.__class__.__name__.replace('RefundStrategy', '').lower()
        return f"Partial {strategy_name.replace('_', ' ').lower()} refund processing completed"
