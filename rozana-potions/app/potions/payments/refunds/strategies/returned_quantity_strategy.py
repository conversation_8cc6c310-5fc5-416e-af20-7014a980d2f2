from decimal import Decimal
from typing import Dict, List, Any

from payments.refunds.strategies.base_strategy import RefundStrategy, RefundCalculationResult
from core.repository.sales_return import sales_return_repository

# Logger
from potions.logging.utils import get_app_logger
logger = get_app_logger('refund_processor_returned_quantity_strategy')


class ReturnedQuantityRefundStrategy(RefundStrategy):
    """Strategy for processing refunds for returned quantities based on return status"""

    def __init__(self):
        super().__init__()
        self.sales_return_repository = sales_return_repository

    def get_refund_calculation(self, order_id: str, **kwargs) -> RefundCalculationResult:
        """
        Calculate refund amount for returned quantities.
        Only processes returns with approved status and pending refund_status.
        """
        log_prefix = self.get_log_prefix()

        return_reference = kwargs.get('return_reference')
        # Get return items that need refunding (approved status with pending refund)
        return_items = self.order_repository.get_return_items_for_refund(str(order_id), return_reference)

        if not return_items:
            logger.info(f"{log_prefix} No pending return items found for refund for order: {order_id}")
            return RefundCalculationResult(
                total_amount=Decimal('0'),
                item_details=[]
            )

        logger.info(f"{log_prefix} Found {len(return_items)} return items for refund")

        # Calculate total refund amount needed
        total_refund_needed = Decimal('0')
        returns_to_process = []

        for item in return_items:
            # Calculate refund amount for this item
            accepted_quantity = Decimal(str(item['accepted_quantity']))
            sale_price = Decimal(str(item['sale_price']))
            item_refund_amount = accepted_quantity * sale_price
            total_refund_needed += item_refund_amount

            return_item_data = {
                'return_item_id': item['return_item_id'],
                'order_item_id': item['order_item_id'],
                'wh_sku': item['wh_sku'],
                'accepted_quantity': accepted_quantity,
                'sale_price': sale_price,
                'refund_amount': item_refund_amount
            }

            returns_to_process.append(return_item_data)

        return RefundCalculationResult(
            total_amount=total_refund_needed,
            item_details=returns_to_process,
            additional_data={'returns_processed': returns_to_process}
        )

    def update_post_refund_status(self, order_id: str, success: bool, item_details: List[Dict[str, Any]], **kwargs) -> None:
        """Update return refund status based on refund success"""
        log_prefix = self.get_log_prefix()
        returns_processed = item_details

        if success:
            # Mark all returns as completed since all refunds were successful
            for return_data in returns_processed:
                self.order_repository.update_return_refund_status(return_data['return_item_id'], 'completed')

                # get current refunded quantity
                order_item_id = return_data['order_item_id']
                current_refunded_quantity = self.order_repository.get_order_item_refunded_quantity(order_id, order_item_id)
                current_refunded_decimal = Decimal(str(current_refunded_quantity))
                new_refunded_quantity = current_refunded_decimal + return_data['accepted_quantity']
                self.order_repository.update_order_item_refunded_quantity(order_item_id, new_refunded_quantity)

            logger.info(f"{log_prefix} Updated refund status to completed for {len(returns_processed)} returns")
        else:
            # Mark returns as partial if some refunds failed
            for return_data in returns_processed:
                self.order_repository.update_return_refund_status(return_data['return_id'], 'partial')
            logger.info(f"{log_prefix} Updated refund status to partial for {len(returns_processed)} returns")

    def get_success_message(self) -> str:
        """Get success message for returned quantity strategy"""
        return "Return quantity refunds processed successfully"

    def get_partial_message(self) -> str:
        """Get partial success message for returned quantity strategy"""
        return "Partial return refund processing completed"
