from decimal import Decimal
from typing import Dict, List, Any

from payments.refunds.strategies.base_strategy import RefundStrategy, RefundCalculationResult
from potions.logging.utils import get_app_logger
from core.repository.sales_return import sales_return_repository

# Logger
from potions.logging.utils import get_app_logger
logger = get_app_logger('refund_processor')


class GrnCallbackRefundStrategy(RefundStrategy):
    """Strategy for processing refunds directly from GRN callback with calculated amount"""

    def __init__(self):
        super().__init__()
        self.sales_return_repository = sales_return_repository

    def get_refund_calculation(self, order_id: str, **kwargs) -> RefundCalculationResult:
        """Get refund calculation from GRN callback parameters"""
        log_prefix = self.get_log_prefix()

        refund_amount = kwargs.get('refund_amount', 0)
        return_reference = kwargs.get('return_reference', '')

        if refund_amount <= 0:
            logger.info(f"{log_prefix} No refund amount needed for order: {order_id}")
            return RefundCalculationResult(
                total_amount=Decimal('0'),
                item_details=[],
                additional_data={'return_reference': return_reference}
            )

        logger.info(f"{log_prefix} GRN refund amount: {refund_amount}, return_ref: {return_reference}")

        # Check if this is a POS order
        is_pos_order = self.is_pos_order(order_id)

        return RefundCalculationResult(
            total_amount=Decimal(str(refund_amount)),
            item_details=[],  # GRN strategy doesn't need item details for calculation
            additional_data={
                'return_reference': return_reference,
                'is_pos_order': is_pos_order
            }
        )

    def update_post_refund_status(self, order_id: str, success: bool, item_details: List[Dict[str, Any]], **kwargs) -> None:
        """Update statuses after GRN refund processing"""
        log_prefix = self.get_log_prefix()
        return_reference = kwargs.get('return_reference', '')

        if success or kwargs.get('refund_results'):  # If any refunds were processed
            try:
                # 1. Update refunded_quantity in order_items table
                self.update_order_items_refunded_quantity(order_id, return_reference)

                # 2. Update refund_status in returns table
                self.update_returns_refund_status(return_reference)

                # 3. Check if complete refund and update payment status if needed
                if success:  # Complete refund successful
                    self.update_payment_status_if_fully_refunded(order_id)

            except Exception as e:
                logger.error(f"{log_prefix} Error updating statuses: {e}", exc_info=True)

    def is_pos_order(self, order_id: str) -> bool:
        """Check if an order is a POS order by examining the order_mode field"""
        try:
            order_data = self.order_repository.get_order_by_id(order_id)
            if order_data:
                order_mode = order_data.get('order_mode', '').lower()
                return order_mode == 'pos'
            return False
        except Exception as e:
            logger.error(f"Error checking if order {order_id} is POS: {e}", exc_info=True)
            return False

    def update_order_items_refunded_quantity(self, order_id: str, return_reference: str):
        """Update refunded_quantity in order_items table based on return items"""
        log_prefix = self.get_log_prefix()
        try:
            # Get return items for this return_reference
            return_items = self.sales_return_repository.get_return_items_by_reference(return_reference) or []

            for item in return_items:
                # Support both GRN payload-style keys and DB-style keys
                sku_code = item.get('sku_code') or item.get('sku')
                returned_qty = item.get('sales_return_quantity', 0) or item.get('quantity_returned', 0)

                if sku_code and returned_qty > 0:
                    # Update refunded_quantity in order_items
                    updated = self.order_repository.increment_order_item_refunded_quantity(
                        order_id=order_id,
                        sku_code=sku_code,
                        increment_qty=returned_qty
                    )
                    if updated:
                        logger.info(f"{log_prefix} Updated refunded_quantity for {sku_code}: +{returned_qty}")
                    else:
                        logger.warning(f"{log_prefix} Failed to update refunded_quantity for {sku_code}")
        except Exception as e:
            logger.error(f"{log_prefix} Error updating order items refunded quantity: {e}", exc_info=True)

    def update_returns_refund_status(self, return_reference: str):
        """Update refund_status in returns table to REFUNDED"""
        log_prefix = self.get_log_prefix()
        try:
            updated = self.sales_return_repository.update_return_refund_status(
                return_reference=return_reference,
                refund_status='REFUNDED'
            )
            if updated:
                logger.info(f"{log_prefix} Updated return refund_status to REFUNDED for {return_reference}")
            else:
                logger.warning(f"{log_prefix} Failed to update return refund_status for {return_reference}")
        except Exception as e:
            logger.error(f"{log_prefix} Error updating return refund status: {e}", exc_info=True)

    def update_payment_status_if_fully_refunded(self, order_id: str):
        """Update payment_status to 53 (REFUNDED) if all payments are fully refunded"""
        log_prefix = self.get_log_prefix()
        try:
            # Get all payments for the order
            all_payments = self.payment_repository.get_payment_details_by_order_id(order_id)
            for payment in all_payments:
                payment_id = payment.get('id')
                payment_amount = Decimal(str(payment.get('payment_amount', 0)))

                # Get total refunded for this payment
                existing_refunds = self.refund_repository.get_refunds_for_payment(payment_id)
                total_refunded = sum(Decimal(str(refund.get('refund_amount', 0))) for refund in existing_refunds)

                # If fully refunded, update payment status to 53 (REFUNDED)
                if total_refunded >= payment_amount:
                    updated = self.payment_repository.update_payment_details_status(
                        payment.get('payment_id'), 
                        53  # PaymentStatus.REFUNDED
                    )
                    if updated:
                        logger.info(f"{log_prefix} Updated payment status to REFUNDED for payment {payment.get('payment_id')}")
                    else:
                        logger.warning(f"{log_prefix} Failed to update payment status for {payment.get('payment_id')}")
        except Exception as e:
            logger.error(f"{log_prefix} Error updating payment status: {e}", exc_info=True)

    def get_success_message(self) -> str:
        """Get success message for GRN strategy"""
        return "GRN refund processed successfully"

    def get_partial_message(self) -> str:
        """Get partial success message for GRN strategy"""
        return "Partial GRN refund processing completed"
