from decimal import Decimal
from typing import Dict, List, Any
from payments.refunds.strategies.base_strategy import RefundStrategy, RefundCalculationResult
from core.repository.sales_return import sales_return_repository


# Logger
from potions.logging.utils import get_app_logger
logger = get_app_logger('refund_processor_unfulfilled_quantity_strategy')


class UnfulfilledQuantityRefundStrategy(RefundStrategy):
    """Strategy for processing refunds based on unfulfilled quantities"""

    def __init__(self):
        super().__init__()
        self.sales_return_repository = sales_return_repository

    def get_refund_calculation(self, order_id: str, **kwargs) -> RefundCalculationResult:
        """
        Calculate refund amount based on unfulfilled quantity.
        Refund formula: (unfulfilled_quantity - cancelled_quantity) 
        Since WMS reports cancelled items as unfulfilled which we've already refunded.
        """
        log_prefix = self.get_log_prefix()

        # Get order items with unfulfilled quantity requiring refund
        items_to_refund = self.order_repository.get_order_items_with_unfulfilled_quantity(order_id)

        if not items_to_refund:
            logger.info(f"{log_prefix} No items with pending unfulfilled quantity refunds for order {order_id}")
            return RefundCalculationResult(
                total_amount=Decimal('0'),
                item_details=[]
            )

        logger.info(f"{log_prefix} Found {len(items_to_refund)} items requiring unfulfilled refunds")

        # Calculate total refund amount needed
        total_refund_needed = Decimal('0')
        item_refunds = []

        for item in items_to_refund:
            unfulfilled_qty = Decimal(str(item.get('unfulfilled_quantity', 0)))
            sale_price = Decimal(str(item.get('sale_price', 0)))

            # Calculate pending unfulfilled refund quantity
            pending_unfulfilled_refund_qty = unfulfilled_qty

            if pending_unfulfilled_refund_qty > 0:
                refund_amount = pending_unfulfilled_refund_qty * sale_price
                total_refund_needed += refund_amount

                item_refunds.append({
                    'item_id': item['id'],
                    'sku': item.get('sku', ''),
                    'wh_sku': item.get('wh_sku', ''),
                    'pending_unfulfilled_refund_qty': float(pending_unfulfilled_refund_qty),
                    'refund_amount': float(refund_amount)
                })

        return RefundCalculationResult(
            total_amount=total_refund_needed,
            item_details=item_refunds
        )

    def update_post_refund_status(self, order_id: str, success: bool, item_details: List[Dict[str, Any]], **kwargs) -> None:
        """Update refunded quantities for all items with proportional calculation"""
        log_prefix = self.get_log_prefix()

        if not success:
            logger.error(f"{log_prefix} Refunds failed for order {order_id}, skipping quantity updates")
            return

        logger.info(f"{log_prefix} Updating refunded quantities for order {order_id}")

        for item in item_details:
            try:
                item_id = item['item_id']
                refunded_qty = item['pending_unfulfilled_refund_qty']

                if not item_id or refunded_qty <= 0:
                    continue

                # Get current refunded quantity and add the newly refunded amount
                current_refunded_qty = self.order_repository.get_order_item_refunded_quantity(order_id, item_id)
                new_refunded_qty = current_refunded_qty + refunded_qty

                # Update database
                if self.order_repository.update_order_item_refunded_quantity(item_id, new_refunded_qty):
                    logger.info(f"{log_prefix} Updated item {item_id}: {current_refunded_qty} -> {new_refunded_qty} (+{refunded_qty})")
                else:
                    logger.error(f"{log_prefix} Failed to update refunded_quantity for item {item_id}")

            except Exception as e:
                logger.error(f"{log_prefix} Error updating refunded quantity for item {item_detail}: {e}")
            