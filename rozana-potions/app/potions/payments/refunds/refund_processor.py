from typing import Dict, Any, List
from pydantic import BaseModel

# Strategies and models
from payments.refunds.strategies.base_strategy import RefundProcessorReturnMessage
from payments.refunds.strategies.cancelled_quantity_strategy import CancelledQuantityRefundStrategy
from payments.refunds.strategies.unfulfilled_quantity_strategy import UnfulfilledQuantityRefundStrategy
from payments.refunds.strategies.grn_callback_strategy import GrnCallbackRefundStrategy
from payments.refunds.strategies.returned_quantity_strategy import ReturnedQuantityRefundStrategy

# Logger
from potions.logging.utils import get_app_logger
logger = get_app_logger('refund_processor')


class RefundProcessor:
    """Simplified refund processor - strategy selection and delegation only"""

    def __init__(self):
        pass

    def create_refund_for_cancelled_quantity(self, order_id: str) -> RefundProcessorReturnMessage:
        """Create refund for cancelled quantities"""
        logger.info(f"Processing refund for cancelled quantity for order {order_id}")
        strategy = CancelledQuantityRefundStrategy()
        result = strategy.process_refund(order_id)
        logger.info(f"Refund for cancelled quantity for order {order_id} processed with result: {result}")
        return result

    def create_refund_for_unfulfilled_quantity(self, order_id: str) -> RefundProcessorReturnMessage:
        """Create refund for unfulfilled quantities"""
        logger.info(f"Processing refund for unfulfilled quantity for order {order_id}")
        strategy = UnfulfilledQuantityRefundStrategy()
        result = strategy.process_refund(order_id)
        logger.info(f"Refund for unfulfilled quantity for order {order_id} processed with result: {result}")
        return result

    def create_refund_from_grn_callback(self, order_id: str, refund_amount: float, return_reference: str = "") -> RefundProcessorReturnMessage:
        """Create refund from GRN callback with specific amount"""
        logger.info(f"Processing refund from GRN callback for order {order_id}")
        strategy = GrnCallbackRefundStrategy()
        result = strategy.process_refund(order_id, refund_amount=refund_amount, return_reference=return_reference)
        logger.info(f"Refund from GRN callback for order {order_id} processed with result: {result}")
        return result

    def create_refund_for_returned_quantity(self, order_id: str, return_reference: str) -> RefundProcessorReturnMessage:
        """Create refund for returned quantities"""
        logger.info(f"Processing refund for returned quantity for order {order_id}")
        strategy = ReturnedQuantityRefundStrategy()
        result = strategy.process_refund(order_id, return_reference=return_reference)
        logger.info(f"Refund for returned quantity for order {order_id} processed with result: {result}")
        return result
