"""
Constants for payment operations - copied from OMS to maintain consistency
"""

class PaymentStatus:
    """Payment status constants - matching OMS exactly"""
    PENDING = 50
    COMPLETED = 51
    FAILED = 52
    REFUNDED = 53
    
    @classmethod
    def get_description(cls, status_code: int) -> str:
        """Get human-readable description of payment status"""
        descriptions = {
            cls.PENDING: "Pending",
            cls.COMPLETED: "Completed", 
            cls.FAILED: "Failed",
            cls.REFUNDED: "Refunded"
        }
        return descriptions.get(status_code, "Unknown")
    
    @classmethod
    def is_final_status(cls, status_code: int) -> bool:
        """Check if status is final (no further changes expected)"""
        return status_code in [cls.COMPLETED, cls.FAILED, cls.REFUNDED]


class RefundStatus:
    """Refund status constants"""
    CREATED = 60
    PENDING = 61
    PROCESSED = 62
    FAILED = 63
    
    @classmethod
    def get_description(cls, status_code: int) -> str:
        """Get human-readable description of refund status"""
        descriptions = {
            cls.CREATED: "Created",
            cls.PENDING: "Pending",
            cls.PROCESSED: "Processed",
            cls.FAILED: "Failed"
        }
        return descriptions.get(status_code, "Unknown")


class OrderStatus:
    """Order status constants - matching OMS exactly"""
    PENDING = 10
    OPEN = 11
    CONFIRMED = 12
    CANCELLED = 13
    COMPLETED = 14

    # WMS processing statuses
    WMS_OPEN = 23
    WMS_INPROGRESS = 24
    WMS_PICKED = 25
    WMS_FULFILLED = 26
    WMS_INVOICED = 27
    WMS_CANCELED = 28

    # TMS statuses
    TMS_SYNCED = 31
    TMS_SYNC_FAILED = 32
    RIDER_ASSIGNED = 33
    TMS_OUT_FOR_DELIVERY = 34
    TMS_DELIVERED = 35
    TMS_RETURN_INITIATED = 36
    TMS_RETURNED = 37

    # WMS status mapping - similar to msk-oms
    WMS_STATUS_MAP = {
        "open": WMS_OPEN,
        "in_progress": WMS_INPROGRESS,
        "picked": WMS_PICKED,
        "partial_fulfilled" : WMS_FULFILLED,
        "fulfilled": WMS_FULFILLED,
        "invoiced": WMS_INVOICED,
        "cancelled": CANCELLED,
        "canceled": CANCELLED,
    }

    @classmethod
    def get_description(cls, status_code: int) -> str:
        """Get human-readable description of order status"""
        descriptions = {
            cls.PENDING: "Pending",
            cls.OPEN: "Open",
            cls.CONFIRMED: "Confirmed",
            cls.CANCELLED: "Cancelled",
            cls.COMPLETED: "Completed",
            cls.WMS_CANCELED: "WMS Cancelled",
            cls.WMS_OPEN: "WMS Open",
            cls.WMS_INPROGRESS: "WMS In Progress",
            cls.WMS_PICKED: "WMS Picked",
            cls.WMS_FULFILLED: "WMS Fulfilled",
            cls.WMS_INVOICED: "WMS Invoiced",
            cls.TMS_SYNCED: "TMS Synced",
            cls.TMS_SYNC_FAILED: "TMS Sync Failed",
            cls.RIDER_ASSIGNED: "Rider Assigned",
            cls.TMS_OUT_FOR_DELIVERY: "TMS Out For Delivery",
            cls.TMS_DELIVERED: "TMS Delivered",
            cls.TMS_RETURN_INITIATED: "TMS Return Initiated",
            cls.TMS_RETURNED: "TMS Returned"
        }
        return descriptions.get(status_code, "Unknown")
